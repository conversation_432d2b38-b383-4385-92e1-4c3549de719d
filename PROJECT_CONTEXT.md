# AI Novel Generator - 项目上下文

## 项目概览
AI Novel Generator 是一个基于 FastAPI 的 AI 辅助小说创作平台，集成了 Web 客户端和完整的后端 API 服务。

## 技术栈
- **后端**: FastAPI + Uvicorn + SQLAlchemy + MySQL
- **前端**: 原生 HTML/JS WebSocket 客户端
- **WebSocket**: websocket-client（同步推理任务发起）+ websockets（异步服务）
- **数据验证**: Pydantic（支持邮箱验证）
- **认证**: JWT + passlib
- **配置**: 自定义配置系统 + PyYAML
- **ID生成**: 雪花算法
- **启动管理**: shell 脚本 + 环境变量

## 项目架构
```
ai-novel/
├── app/                    # 主应用目录
│   ├── config/             # 配置系统
│   │   ├── application.yaml    # YAML配置文件
│   │   ├── configuration.py    # 配置管理器（@config系统）
│   │   └── database.py         # 数据库配置
│   ├── models/             # 数据模型
│   │   ├── common.py           # 统一API响应格式
│   │   ├── novel.py            # 小说相关模型
│   │   ├── mixins.py           # 数据库表混入类
│   │   └── auth.py             # 认证模型
│   ├── routers/            # API路由
│   │   ├── novels_router.py           # 小说管理API
│   │   ├── client_router.py           # Web客户端API
│   │   ├── auth_router.py             # 认证API
│   │   ├── config_router.py           # 配置API
│   │   ├── story_structure_router.py
│   │   ├── chapter_structure_router.py
│   │   ├── outline_router.py
│   │   ├── chapter_content_router.py
│   │   ├── callbacks_router.py
│   │   └── extract_router.py
│   ├── services/           # 业务逻辑
│   │   ├── novel_service.py
│   │   ├── auth_service.py
│   │   └── [生成相关服务]
│   ├── utils/              # 工具函数
│   │   ├── snowflake.py        # 雪花ID生成器
│   │   ├── inference_websocket.py  # WebSocket推理工具（合并）
│   │   ├── error_handlers.py   # 统一错误处理
│   │   ├── base_service.py     # 基础服务类
│   │   ├── auth.py             # 认证工具
│   │   └── user_context.py     # 用户上下文
│   ├── middleware/         # 中间件
│   │   └── auth_middleware.py  # JWT认证中间件
│   └── main.py             # 应用入口
├── client/                 # Web客户端（LLM推理服务客户端）
│   ├── index.html          # 主页面
│   ├── server.js           # Node.js服务器
│   ├── package.json        # Node.js配置
│   └── README.md           # 客户端说明
├── scripts/                # 管理脚本
│   └── run.sh              # 启动/管理脚本
├── logs/                   # 日志目录
├── prompts/                # AI提示词模板
├── requirements.txt        # Python依赖
├── README.md               # 项目文档
└── PROJECT_CONTEXT.md      # 项目上下文（本文件）
```

## 核心架构特性

### 1. 统一配置系统（@config）
**位置**: `app/config/configuration.py`
**用法**: 
```python
from app.config.configuration import config
# 基础访问
jwt_secret = config("jwt.secret_key", "default")
# 嵌套访问
db_host = config("database.host", "localhost")
# 业务配置
threshold = config("chapter_grouping.extra_character_threshold", 36)
```

### 2. 智能启动系统
**脚本**: `scripts/run.sh`
**环境变量**: PORT（默认3300）、HOST（默认0.0.0.0）
**命令**:
```bash
bash scripts/run.sh          # 默认启动
bash scripts/run.sh start    # 启动
bash scripts/run.sh stop     # 停止
bash scripts/run.sh restart  # 重启
bash scripts/run.sh status   # 状态
bash scripts/run.sh monitor  # 监控
bash scripts/run.sh clean    # 清理缓存
```

### 3. Web客户端集成
**静态文件挂载**: `app.mount("/client", StaticFiles(directory="client", html=True))`
**访问路径**:
- `/web` → 重定向到客户端
- `/client/` → 直接访问静态文件
- `/client/index.html` → 主页面

### 4. 统一错误处理
**装饰器**: `@handle_api_errors`
**响应格式**: `ApiResponse[T]`
**异常类**: `NovelAPIError`、`ValidationError`

### 5. 混入类架构
**基类**: `IdMixin`、`TimestampMixin`、`UserTrackingMixin`、`StatusMixin`
**使用**:
```python
class MyTable(Base, IdMixin, TimestampMixin):
    __tablename__ = "my_table"
    # 自动包含: id, created_at, updated_at
```

### 6. 基础服务类
**模式**: 继承 `BaseService[ModelType]`
**自动方法**: get_by_id、create、update、delete、get_list、get_paginated_response

### 7. 雪花ID系统
**生成器**: `app/utils/snowflake.py`
**使用**: `from app.utils.snowflake import get_next_id`

### 8. WebSocket推理系统
**位置**: `app/utils/inference_websocket.py`
**功能**: 与外部WebSocket推理服务的集成
**架构模式**:
```python
# 新的推理流程
1. HTTP POST创建任务 → 返回 {"task_id": "xxx", "websocket_url": "ws://...", "subscribe_action": "subscribeTask"}
2. 用户使用WebSocket连接 → ws.connect(websocket_url)
3. 发送订阅请求 → {"action": "subscribeTask", "taskId": "xxx"}
4. 接收推理结果 → {"type": "token", "content": "..."} 或 {"type": "done"}
```

**任务分离设计**:
- **API服务责任**: 发起推理任务，获取taskId，返回给用户
- **用户责任**: 使用taskId连接WebSocket服务，监听推理过程
- **推理服务责任**: 处理实际的AI推理逻辑

**消息格式**:
```json
{
  "novel_id": 1,
  "novel_title": "我的仙侠小说",
  "story_structure_id": 9202644106743808,
  "created_at": "2025-06-17T13:52:23",
  "task_id": "17819329309638707",
  "websocket_url": "ws://localhost:8080/ws",
  "subscribe_action": "subscribeTask"
}
```

**响应流程**:
1. 连接WebSocket → 收到 `{"type": "connected"}`
2. 发送推理请求 → 收到 `{"type": "taskCreated", "taskId": "..."}`
3. 断开连接，返回taskId给用户

## 数据库架构

### 主要数据表
- **novels**: 小说主表（id, title, config, status, created_at, updated_at, user_id）
- **users**: 用户表（认证系统）
- **story_structures**: 故事结构表
- **chapter_structures**: 章节结构表
- **outlines**: 大纲表
- **chapter_contents**: 章节内容表

### 配置存储
小说配置以JSON格式存储在 `novels.config` 字段中：
```json
{
  "genre": "仙侠",
  "cultivation_system": "传统修仙",
  "target_reader": "male",
  "story_length": "medium",
  "writing_elements": "仙侠+系统"
}
```

## API设计模式

### 统一响应格式
```json
{
  "success": true|false,
  "message": "操作描述",
  "data": {}, // 或 null
  "error_code": "ERROR_CODE", // 或 null
  "timestamp": "2025-01-13T..."
}
```

### 分页响应
```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "total": 100,
      "limit": 10,
      "offset": 0,
      "has_more": true
    }
  }
}
```

### 路由设计模式
- 前缀: `/api/v1`
- 资源路径: `/novels/{id}`
- 操作路径: `/novels/{id}/generate/story-structure`

## 重要代码模式

### 1. 路由定义模板
```python
from fastapi import APIRouter, Depends
from app.utils.error_handlers import handle_api_errors
from app.models.common import ApiResponse

router = APIRouter()

@router.get("/endpoint", response_model=ApiResponse[ReturnType])
@handle_api_errors
async def endpoint_function():
    # 业务逻辑
    return ApiResponse[ReturnType](
        success=True,
        message="操作成功",
        data=result
    )
```

### 2. 服务类模板
```python
from app.utils.base_service import BaseService
from app.models.my_model import MyModelDB

class MyService(BaseService[MyModelDB]):
    def __init__(self, db: Session):
        super().__init__(db, MyModelDB)
    
    def custom_method(self, param):
        # 自定义业务逻辑
        return self.get_by_id_or_raise(param)
```

### 3. 数据模型模板
```python
from app.models.mixins import IdMixin, TimestampMixin
from sqlalchemy import Column, String, JSON

class MyModelDB(Base, IdMixin, TimestampMixin):
    __tablename__ = "my_table"
    
    name = Column(String(100), nullable=False)
    config = Column(JSON)
```

## 配置文件结构 (application.yaml)

```yaml
# JWT认证
jwt:
  secret_key: "your-secret-key"
  access_token_expire_minutes: 30
  development_mode: true
  auto_login_first_user: true

# 数据库
database:
  host: "localhost"
  port: 3306
  name: "ai_novel"

# 业务配置
target_readers:
  male: "男性读者"
  female: "女性读者"
  general: "通用读者"

novel_genres:
  xianxia:
    name: "仙侠"
    description: "修仙题材小说"

cultivation_systems:
  traditional:
    name: "传统修仙"
    levels: ["炼气", "筑基", "金丹", "元婴"]
```

## 最近更新和改进

### v2.0 (2024-06-18) - 推理服务回调系统升级 🆕

#### 🔧 **回调格式更新**
支持推理服务端的新回调格式，新增中间状态支持：

**新增状态类型**：
- `success` - 最终完成状态（保持不变）
- `failed` - 失败状态（保持不变）
- `completed_section` - 中间进度状态（🆕新增）

**新增字段**：
- `original_task_id` - 原始任务ID
- `section_type` - 章节类型（completed_section状态时使用）
- `promptTokens` - 提示词Token数量
- `completionTokens` - 生成内容Token数量  
- `totalTokens` - 总Token数量

**处理逻辑**：
- 中间状态仅记录日志，不更新数据库最终状态
- 中间状态不触发外部callback_url通知
- 任务继续执行直到收到最终状态

#### 📁 **修改的文件**：
- `app/routers/callbacks_router.py` - 更新CallbackRequest模型和处理逻辑
- `CALLBACK_SYSTEM.md` - 更新文档说明新的回调格式

#### 🐛 **解决的问题**：
修复了 "未知的状态: completed_section" 错误，现在系统能正确处理推理服务的中间状态回调。

### v2.1 (2024-06-18) - 章节结构回调逻辑优化 🆕

#### 🔧 **回调处理逻辑重构**
根据用户需求优化了章节结构回调的处理方式：

**新的处理流程**：
1. **`completed_section` 中间状态**：
   - 每个中间状态内容单独入库到 `chapter_structure_forks` 表
   - 自动分配递增的分支索引（`fork_index`）
   - 智能提取标题（从 markdown 标题或生成默认标题）
   - 实时保存，避免数据丢失

2. **`success` 最终状态**：
   - 只更新 `chapter_structures` 任务状态为 `completed`
   - 设置 `is_complete = "true"` 和 `completed_at` 时间戳
   - 触发外部回调（如果配置了 callback_url）
   - 不再重复处理分支数据（避免重复入库）

**新增服务方法**：
- `create_single_chapter_structure_fork()`: 处理单个中间状态分支入库
- `update_chapter_structure_task_status()`: 只更新任务状态，不处理分支

**优化效果**：
- **实时保存**: 推理过程中的内容立即保存，提高数据安全性
- **避免重复**: 防止最终回调时重复创建分支记录
- **清晰分离**: 中间状态处理和最终状态处理逻辑完全分离
- **性能优化**: 减少最终回调时的数据处理量

**修改的文件**：
- `app/routers/callbacks_router.py` - 更新回调处理逻辑
- `app/services/chapter_structure_service.py` - 新增分支处理方法
- `CALLBACK_SYSTEM.md` - 更新文档说明

### v2.1.1 (2024-06-18) - 累积内容处理修复 🔧

#### 🐛 **累积回调内容处理问题修复**
解决了推理服务回调累积内容的重复入库问题：

**问题现象**：
- 第1次回调：包含大纲1
- 第2次回调：包含大纲1 + 大纲2 
- 第3次回调：包含大纲1 + 大纲2 + 大纲3
- 导致重复内容被多次入库

**解决方案**：
1. **智能内容提取**：
   ```python
   def _extract_latest_section_from_cumulative_content(content, existing_fork_count):
       # 从配置中获取全局分隔符（默认为§§）
       delimiter = config("content_parsing.section_delimiter", "§§")
       sections = content.split(delimiter)  # 按配置的分隔符拆分
       valid_sections = [s.strip() for s in sections if s.strip()]
       
       # 获取下一个新章节
       if len(valid_sections) > existing_fork_count:
           return valid_sections[existing_fork_count]  # 提取最新部分
       else:
           return valid_sections[-1]  # 返回最后一个章节
   ```

2. **标题清理**：
   - 自动移除特殊标识符：配置化分隔符、`📖`
   - 处理多级markdown标题：`###`、`##`、`#`
   - 智能提取包含"大纲"的标题行
   - 清理后的标题更加简洁易读

3. **避免重复入库**：
   - 只保存最新增加的章节内容
   - 基于已有分支数量计算新内容索引
   - 确保每个章节只入库一次

**修改的文件**：
- `app/services/chapter_structure_service.py` - 新增内容提取和标题清理方法
- `CALLBACK_SYSTEM.md` - 更新累积内容处理说明

**优化效果**：
- ✅ 避免重复内容入库
- ✅ 标题更加简洁易读  
- ✅ 智能识别新增章节
- ✅ 提高数据质量和存储效率

## 开发最佳实践

### 代码组织
- 每个功能模块独立的路由、服务、模型
- 使用依赖注入模式
- 统一的错误处理和响应格式

### 配置管理
- 所有配置集中在 application.yaml
- 使用 config() 函数访问配置
- 支持默认值和环境变量覆盖

### 数据库设计
- 使用混入类消除重复字段
- 雪花ID作为主键
- JSON字段存储复杂配置

### API设计
- RESTful设计原则
- 统一的响应格式
- 完整的错误处理

### WebSocket推理集成
- 基于demo标准实现推理流程
- 后台线程处理，非阻塞主逻辑
- 完整的状态管理和错误恢复
- 支持单轮和多轮对话格式

## 常见开发任务

### 1. 添加新的API端点
```python
# 1. 在 routers/ 中创建路由文件 (建议命名为 *_router.py)
@router.post("/novels", response_model=ApiResponse[Novel])
@handle_api_errors
async def create_novel(novel_data: NovelCreate, service: NovelService = Depends()):
    novel = service.create(novel_data.dict())
    return ApiResponse[Novel](success=True, data=novel)

# 2. 在 main.py 中注册路由
app.include_router(router, prefix="/api/v1", tags=["novels"])
```

### 2. 添加新的数据表
```python
# 1. 在 models/ 中定义模型
class NewTableDB(Base, IdMixin, TimestampMixin):
    __tablename__ = "new_table"
    name = Column(String(100), nullable=False)

# 2. 在 services/ 中创建服务
class NewService(BaseService[NewTableDB]):
    def __init__(self, db: Session):
        super().__init__(db, NewTableDB)
```

### 3. 添加新的配置项
```yaml
# 在 application.yaml 中添加
new_feature:
  enabled: true
  threshold: 100

# 在代码中使用
enabled = config("new_feature.enabled", False)
```

### 4. 集成WebSocket推理服务（新的标准实现）
```python
# 1. 在服务类中发起推理任务
from app.utils.inference_websocket import InferenceWebSocketManager

class MyService:
    def generate_content(self, novel_id: int):
        # 生成提示词
        prompt = self.build_prompt(novel_id)
        
        # 保存到数据库
        record = self.save_to_db(novel_id, prompt)
        
        # 生成WebSocket连接信息
        ws_info = InferenceWebSocketManager.generate_connection_info(
            task_id=str(record.id)
        )
        
        # 发起推理任务（新的实现）
        websocket_task_id = InferenceWebSocketManager.start_inference_task(
            ws_url=ws_info.url,
            prompt=prompt,  # 简单的prompt字符串
            task_id=str(record.id)
        )
        
        # 返回给用户
        return {
            "record_id": record.id,
            "websocket_task_id": websocket_task_id,  # 已确认推理开始
            "status": "推理已启动" if websocket_task_id else "推理启动失败",
            "wss_url": ws_info.model_dump()  # 用户可选择监听推理过程
        }

# 2. 用户使用返回的信息监听推理结果（可选）
# 前端JavaScript代码示例：
# const ws = new WebSocket(wss_url.url);
# ws.onopen = () => ws.send(JSON.stringify({
#     action: 'subscribeTask',
#     taskId: websocket_task_id
# }));
```

## 启动和部署

### 开发环境启动
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 配置数据库（编辑 application.yaml）

# 3. 启动应用
bash scripts/run.sh  # 端口3300

# 4. 访问
# API文档: http://localhost:3300/docs
# Web客户端: http://localhost:3300/web
```

### WebSocket推理测试
```bash
# 快速测试
cd demo && python quick_demo.py

# 完整测试
cd demo && python python_client_demo.py --prompt "你好"

# API集成测试
python -c "
from app.utils.inference_websocket import InferenceWebSocketManager
ws_info = InferenceWebSocketManager.generate_connection_info('test')
task_id = InferenceWebSocketManager.start_inference_task(
    ws_url=ws_info.url, prompt='写一首诗', task_id='test'
)
print(f'任务ID: {task_id}')
"
```

### 生产环境
```bash
# 设置生产环境变量
export ENV=production
export PORT=80
export HOST=0.0.0.0

# 启动
bash scripts/run.sh
```

## 常见Bug修复模式

### 1. 枚举值访问错误
**错误**: `'str' object has no attribute 'value'`
**原因**: 代码假设字段是枚举对象，但实际可能是字符串或None
**修复模式**:
```python
def _safe_get_enum_value(self, enum_field):
    """安全获取枚举值"""
    if enum_field is None:
        return '默认值'
    elif isinstance(enum_field, str):
        return enum_field
    elif hasattr(enum_field, 'value'):
        return enum_field.value
    else:
        return '未知'
```

### 2. 命名冲突错误
**错误**: `'ConfigObject' object is not callable`
**原因**: 局部变量与导入的函数同名
**修复模式**:
```python
# 错误写法
config = some_config_object
result = config("some.key", "default")  # config被覆盖

# 正确写法
novel_config = some_config_object
result = config("some.key", "default")  # 使用全局config函数
```

### 3. WebSocket推理相关错误
**错误**: `推理任务创建后立即断开，没有实际开始推理`
**原因**: 之前的实现在收到taskCreated后立即断开连接
**修复方案**: 
```python
# 错误的实现（已修复）
elif message_type == "taskCreated":
    self.task_id = data.get("taskId")
    ws.close()  # ❌ 立即断开，推理无法开始

# 正确的实现
elif message_type == "taskCreated":
    self.task_id = data.get("taskId")
    # 继续等待推理开始，不断开连接

elif message_type == "token":
    if not self.inference_started:
        self.inference_started = True  # ✅ 确认推理开始
        # 现在可以安全断开连接
```

### 4. 数据库字段错误
**错误**: `'field_name' is an invalid keyword argument`
**原因**: 尝试设置数据库模型中不存在的字段
**修复步骤**:
1. 检查数据库模型定义
2. 确认字段名拼写和类型
3. 检查是否需要数据库迁移

### 5. 类型不匹配错误
**错误**: Boolean字段传入字符串值
**修复模式**:
```python
# 错误
is_complete = "true"  # 字符串

# 正确
is_complete = True    # 布尔值
```

### 6. 配置项格式不一致
**问题**: 不同配置项返回不同的数据结构
**统一格式**:
```python
# 统一使用 Dict[str, str] 格式
{
  "novel_genres": {"key": "name", ...},
  "cultivation_systems": {"key": "name", ...},
  "romance_protagonists": {"key": "name", ...}
}
```

## 故障排除

### 常见问题
1. **端口被占用**: 脚本会自动检测，提示是否强制释放
2. **虚拟环境问题**: 脚本自动检测并激活 `venv/`
3. **配置文件错误**: 检查 `application.yaml` 语法
4. **数据库连接失败**: 检查数据库配置和服务状态
5. **WebSocket推理失败**: 检查推理服务是否在线 (`**************:3000`)

### 日志查看
```bash
# 应用日志
tail -f logs/app.log

# 启动日志
bash scripts/run.sh status
```

### 调试模式
```bash
# 开发模式（自动重载）
python -m app.main

# 清理缓存
bash scripts/run.sh clean

# WebSocket推理调试
cd demo && python quick_demo.py
```

## Web客户端使用

### 功能特点
- **WebSocket连接**: 实时双向通信
- **多轮对话**: 支持上下文保持
- **流式回复**: 实时显示生成内容
- **任务管理**: 任务状态跟踪
- **重连机制**: 自动重连断开的连接

### 配置项
- 服务器地址（默认: **************）
- 端口号（默认: 3000）
- 连接超时设置
- 自动重连选项

### 独立运行
```bash
cd client
npm start  # 默认端口3000
```

这个项目上下文应该能够让新的对话快速理解项目结构、开发模式和常见任务。

## 最近更新记录

### 2025-06-17: HTTP推理系统重构与API格式优化

#### 1. HTTP推理方式重构
**问题**: 复杂的WebSocket推理逻辑导致连接不稳定，维护困难
**解决方案**:
- 彻底重构为HTTP创建任务+WebSocket订阅的模式
- 简化架构：HTTP POST创建任务，返回taskId供用户自行订阅
- 移除复杂的WebSocket连接逻辑，专注于HTTP请求的稳定性

**修改文件**:
- `app/utils/inference_websocket.py`: 重写为纯HTTP客户端
- `app/services/story_structure_service.py`: 使用新的HTTP API
- `app/config/application.yaml`: 更新配置为HTTP推理配置

**新的工作流程**:
```python
# 1. HTTP创建任务
response = InferenceManager.create_inference_task([
    {"role": "user", "content": "写一首诗"}
])

# 2. 返回订阅信息
{
    "task_id": "17819329309638707",
    "websocket_url": "ws://localhost:8080/ws", 
    "subscribe_action": "subscribeTask"
}

# 3. 用户自行WebSocket订阅
ws.send(JSON.stringify({
    "action": "subscribeTask",
    "taskId": "17819329309638707"
}))
```

#### 2. HTTP重试机制实现
**特点**: HTTP请求比WebSocket连接更稳定可靠
**配置**:
```yaml
http_inference:
  host: "127.0.0.1"
  port: 8080
  timeout: 30
  max_retries: 3
  retry_delay: 2
```

**重试逻辑**:
- 针对网络错误（超时、连接错误）进行重试
- 服务端业务错误不重试，立即返回
- 支持配置重试次数和间隔

#### 3. API返回格式统一
**统一后的响应格式**:
```json
{
  "success": true,
  "message": "故事结构生成成功",
  "data": {
    "novel_id": 1,
    "generation_type": "story_structure", 
    "content": {
      "novel_id": 1,
      "novel_title": "我的仙侠小说",
      "story_structure_id": 9202644106743808,
      "created_at": "2025-06-17T13:52:23",
      "task_id": "17819329309638707",
      "websocket_url": "ws://localhost:8080/ws",
      "subscribe_action": "subscribeTask"
    },
    "is_complete": false
  }
}
```

#### 4. 向后兼容处理
**保留的接口**:
- `InferenceWebSocketManager`: 重定向到新的HTTP实现
- 所有旧的方法名保持不变，内部使用HTTP方式

#### 5. 测试验证
```bash
✅ HTTP任务创建成功 - task_id: 17819329309638707
✅ 返回WebSocket订阅信息完整
✅ 用户可自行处理WebSocket订阅
✅ API响应格式统一
```

**关键改进**:
- **简化架构**: 去除复杂的WebSocket推理逻辑
- **提高稳定性**: HTTP请求比WebSocket连接更可靠
- **用户友好**: 返回完整订阅信息，用户可灵活处理
- **易于维护**: 代码结构简洁清晰

**智能提取逻辑**:
```python
# 系统会自动提取最新的章节部分
def _extract_latest_section_from_cumulative_content(content, existing_fork_count):
    # 从配置中获取全局分隔符（默认为§§）
    delimiter = config("content_parsing.section_delimiter", "§§")
    sections = content.split(delimiter)  # 按配置的分隔符拆分
    valid_sections = [s.strip() for s in sections if s.strip()]
    
    # 获取下一个新章节
    if len(valid_sections) > existing_fork_count:
        return valid_sections[existing_fork_count]  # 提取最新部分
    else:
        return valid_sections[-1]  # 返回最后一个章节
```

**配置化全局分隔符** 🆕:
- **配置位置**: `app/config/application.yaml`
- **配置项**: `content_parsing.section_delimiter`
- **默认值**: `§§`
- **应用范围**: 全局所有需要内容分割的场景
- **自定义示例**: 
  ```yaml
  content_parsing:
    section_delimiter: "---SECTION---"  # 自定义全局分隔符
  ```

**标题清理**:
- 自动移除特殊标识符：配置化分隔符、`📖`
- 处理多级markdown标题：`###`、`##`、`#`
- 智能提取包含"大纲"的标题行
- 清理后的标题更加简洁易读

### v2025.06 - 章节内容生成系统完整实现 🆕

#### 🔧 **新增章节内容路由**
完整实现了章节内容生成的API系统：

**核心功能**：
- **章节内容生成**: 基于大纲分支生成小说正文内容
- **HTTP推理集成**: 使用system+user消息格式调用推理服务
- **外部回调支持**: 完整的callback_url机制，支持任务完成通知
- **配置化参数**: 从application.yaml读取生成参数

**API端点**：
- `POST /api/v1/novels/{novel_id}/generate/chapter-content` - 生成章节内容
- `GET /api/v1/novels/{novel_id}/chapter-content` - 查询单个章节内容
- `GET /api/v1/novels/{novel_id}/chapter-contents` - 查询整个小说的章节内容列表
- `GET /api/v1/novels/{novel_id}/outline-tasks/{outline_task_id}/chapter-contents` - 查询大纲的章节内容列表

#### 📁 **修改的文件**：
- `app/models/novel.py` - 更新ChapterContentDB模型匹配数据库表结构
- `app/services/chapter_content_service.py` - 完整的章节内容服务实现
- `app/routers/chapter_content_router.py` - 新增章节内容路由
- `app/routers/callbacks_router.py` - 更新章节内容回调处理
- `app/main.py` - 注册新路由
- `app/config/application.yaml` - 添加章节内容生成配置

#### 🔧 **数据库模型更新**
`ChapterContentDB` 模型完全匹配数据库表结构：
```python
class ChapterContentDB(Base, IdMixin, TimestampMixin, UserTrackingMixin):
    __tablename__ = "chapter_contents"
    
    novel_id = Column(BigInteger, nullable=False, comment="小说ID（冗余字段，便于查询）")
    outline_fork_id = Column(BigInteger, nullable=False, comment="大纲分支ID（关联outline_forks表）")
    outline_forks_index = Column(BigInteger, nullable=False, comment="章节序号（从大纲分支fork_index获得）")
    outline_forks_title = Column(String(200), nullable=False, comment="章节标题")
    outline_type = Column(String(50), nullable=True, comment="内容类型")
    generated_prompt = Column(Text, nullable=False, comment="生成的提示词内容")
    parameters = Column(Text, nullable=False, comment="生成参数（JSON格式）")
    template_info = Column(Text, nullable=False, comment="模板信息（JSON格式）")
    status = Column(String(20), nullable=True, comment="生成状态：pending/running/completed/failed")
    is_complete = Column(String(10), nullable=True, comment="是否完成")
    error_message = Column(Text, nullable=True, comment="错误信息（如果失败）")
    chapter_inference = Column(Text, nullable=True, comment="AI推理生成的章节文本内容")
    websocket_url = Column(String(500), nullable=True, comment="WebSocket连接URL")
    websocket_task_id = Column(String(100), nullable=True, comment="WebSocket任务ID")
    callback_url = Column(String(500), nullable=True, comment="外部回调URL")
    word_count = Column(BigInteger, nullable=True, comment="实际字数")
    completed_at = Column(DateTime, nullable=True, comment="完成时间")
```

#### ⚙️ **新增配置项**
```yaml
# 章节内容生成配置
chapter_content:
  default_style: "default"         # 默认写作风格
  default_word_count: 6000        # 默认目标字数
  default_pov_character: null     # 默认视角角色（null表示自动选择）
```

#### 🚀 **推理服务集成**
- **System+User格式**: 使用标准的对话消息格式
- **HTTP推理**: 通过InferenceManager创建推理任务
- **WebSocket订阅**: 返回订阅信息供用户监听推理过程
- **自动回调**: 推理完成后自动更新数据库状态

#### 🔄 **回调机制**
支持统一的CallbackRequest格式：
```json
{
    "task_id": "123456789",
    "task_type": "chapter_content",
    "status": "success",  // 或 "failed"
    "result": "生成的章节内容...",  // 成功时包含
    "error_message": "错误描述",  // 失败时包含
    "timestamp": 1699999999.999
}
```

#### 🛡️ **数据验证和安全**
- **必需字段验证**: 确保outline_forks_index和outline_forks_title不为空
- **字符串清理**: 自动清理标题中的多余空格
- **API数据过滤**: 不返回内部参数（generated_prompt、parameters、template_info）
- **错误处理**: 完整的异常处理和日志记录

#### 📋 **API使用示例**
```bash
# 生成章节内容
POST /api/v1/novels/1001/generate/chapter-content?outline_forks_id=12345&callback_url=https://your-domain.com/webhook/chapter-complete

# 查询章节内容
GET /api/v1/novels/1001/chapter-content?outline_forks_id=12345

# 查询小说所有章节内容
GET /api/v1/novels/1001/chapter-contents

# 查询大纲任务的章节内容
GET /api/v1/novels/1001/outline-tasks/67890/chapter-contents
```

#### 🎯 **技术特点**
- ✅ **完整的生成流程**: 故事结构 → 章节结构 → 大纲 → 章节内容
- ✅ **配置化管理**: 所有参数可通过application.yaml配置
- ✅ **外部回调支持**: 支持第三方系统集成
- ✅ **数据库完全匹配**: 模型与实际表结构一致
- ✅ **安全的API设计**: 不暴露内部实现细节
- ✅ **统一架构**: 遵循项目现有的分层架构模式