#!/bin/bash

# AI Novel Generator 启动脚本
# 功能：启动、监控、重启应用，管理端口占用
# 支持单进程和多进程部署模式

# ==================== 配置变量 ====================
APP_MODULE="app.main:app"
HOST="0.0.0.0"
PORT=3300
LOG_FILE="./logs/app.log"

# 部署模式配置
WORKERS=${WORKERS:-"auto"}                 # 工作进程数，默认auto（自动计算）
DEPLOYMENT_MODE=${DEPLOYMENT_MODE:-"development"}  # development/production

# 回调服务配置（可通过环境变量覆盖，默认auto自动计算）
CALLBACK_MAX_WORKERS=${CALLBACK_MAX_WORKERS:-"auto"}
CALLBACK_POOL_CONNECTIONS=${CALLBACK_POOL_CONNECTIONS:-"auto"}
CALLBACK_POOL_MAXSIZE=${CALLBACK_POOL_MAXSIZE:-"auto"}
CALLBACK_MAX_CONCURRENT=${CALLBACK_MAX_CONCURRENT:-"auto"}

# ==================== 系统资源检测 ====================

# 获取CPU核心数
get_cpu_cores() {
  if command -v nproc >/dev/null 2>&1; then
    nproc
  elif [ -f /proc/cpuinfo ]; then
    grep -c ^processor /proc/cpuinfo
  elif command -v sysctl >/dev/null 2>&1; then
    sysctl -n hw.ncpu 2>/dev/null || echo "4"
  else
    echo "4"  # 默认值
  fi
}

# 获取总内存（MB）
get_total_memory() {
  if [ -f /proc/meminfo ]; then
    # Linux
    awk '/MemTotal:/ {print int($2/1024)}' /proc/meminfo
  elif command -v sysctl >/dev/null 2>&1; then
    # macOS
    local mem_bytes=$(sysctl -n hw.memsize 2>/dev/null)
    if [ -n "$mem_bytes" ]; then
      echo $((mem_bytes / 1024 / 1024))
    else
      echo "8192"  # 默认8GB
    fi
  else
    echo "8192"  # 默认8GB
  fi
}

# 获取可用内存（MB）
get_available_memory() {
  if [ -f /proc/meminfo ]; then
    # Linux - 获取可用内存
    awk '/MemAvailable:/ {print int($2/1024)}' /proc/meminfo
  elif command -v vm_stat >/dev/null 2>&1; then
    # macOS - 计算可用内存
    local page_size=$(vm_stat | grep "page size" | awk '{print $8}' | tr -d ':')
    local free_pages=$(vm_stat | grep "Pages free" | awk '{print $3}' | tr -d '.')
    local inactive_pages=$(vm_stat | grep "Pages inactive" | awk '{print $3}' | tr -d '.')
    
    if [ -n "$page_size" ] && [ -n "$free_pages" ] && [ -n "$inactive_pages" ]; then
      echo $(((free_pages + inactive_pages) * page_size / 1024 / 1024))
    else
      # 如果无法获取，返回总内存的70%作为估算
      echo $(($(get_total_memory) * 70 / 100))
    fi
  else
    # 返回总内存的70%作为估算
    echo $(($(get_total_memory) * 70 / 100))
  fi
}

# 检测系统负载
get_system_load() {
  if command -v uptime >/dev/null 2>&1; then
    # 获取1分钟平均负载
    uptime | awk -F'load average:' '{print $2}' | awk -F',' '{print $1}' | xargs
  else
    echo "0.0"
  fi
}

# ==================== 智能资源分配 ====================

# 计算最优工作进程数
calculate_optimal_workers() {
  local cpu_cores=$(get_cpu_cores)
  local total_memory=$(get_total_memory)
  local available_memory=$(get_available_memory)
  local system_load=$(get_system_load)
  
  echo "系统资源检测:" >&2
  echo "  - CPU核心数: $cpu_cores" >&2
  echo "  - 总内存: ${total_memory}MB" >&2
  echo "  - 可用内存: ${available_memory}MB" >&2
  echo "  - 系统负载: $system_load" >&2
  
  # 基于CPU核心数的基础计算
  local cpu_based_workers=$cpu_cores
  
  # 基于内存的限制（每个worker估算需要200-500MB内存）
  local memory_based_workers=$((available_memory / 300))
  
  # 根据部署模式调整
  if [ "$DEPLOYMENT_MODE" = "production" ]; then
    # 生产环境：更保守，考虑系统稳定性
    cpu_based_workers=$((cpu_cores - 1))
    memory_based_workers=$((available_memory / 400))  # 更保守的内存分配
  else
    # 开发环境：可以更激进一些
    cpu_based_workers=$cpu_cores
    memory_based_workers=$((available_memory / 250))
  fi
  
  # 考虑系统负载
  local load_int=$(echo "$system_load" | cut -d'.' -f1)
  if [ -n "$load_int" ] && [ "$load_int" -gt "$cpu_cores" ]; then
    echo "  - 警告: 系统负载较高 ($system_load)，减少进程数" >&2
    cpu_based_workers=$((cpu_based_workers / 2))
  fi
  
  # 取CPU和内存限制的最小值
  local optimal_workers=$cpu_based_workers
  if [ "$memory_based_workers" -lt "$optimal_workers" ]; then
    optimal_workers=$memory_based_workers
  fi
  
  # 最低保证2个工作进程
  if [ "$optimal_workers" -lt 2 ]; then
    optimal_workers=2
  fi
  
  # 最高限制16个工作进程（避免过多进程导致调度开销）
  if [ "$optimal_workers" -gt 16 ]; then
    optimal_workers=16
  fi
  
  echo "  - CPU建议进程数: $cpu_based_workers" >&2
  echo "  - 内存建议进程数: $memory_based_workers" >&2
  echo "  - 最终建议进程数: $optimal_workers" >&2
  
  echo "$optimal_workers"
}

# 计算回调服务参数
calculate_callback_params() {
  local workers=$1
  local cpu_cores=$(get_cpu_cores)
  local total_memory=$(get_total_memory)
  
  # 确保workers是数字
  if ! [[ "$workers" =~ ^[0-9]+$ ]]; then
    echo "错误：工作进程数不是有效数字: '$workers'" >&2
    workers=1
  fi

  # 单进程模式
  if [ "$workers" -eq 1 ]; then
    CALC_CALLBACK_MAX_WORKERS=$((cpu_cores / 2))
    CALC_CALLBACK_POOL_CONNECTIONS=20
    CALC_CALLBACK_MAX_CONCURRENT=50
  else
    # 多进程模式：每个进程分配更少资源
    CALC_CALLBACK_MAX_WORKERS=$(((cpu_cores + workers - 1) / workers))  # 向上取整
    CALC_CALLBACK_POOL_CONNECTIONS=$((20 / workers))
    CALC_CALLBACK_MAX_CONCURRENT=$((50 / workers))
  fi
  
  # 设置合理范围
  if [ "$CALC_CALLBACK_MAX_WORKERS" -lt 2 ]; then
    CALC_CALLBACK_MAX_WORKERS=2
  elif [ "$CALC_CALLBACK_MAX_WORKERS" -gt 10 ]; then
    CALC_CALLBACK_MAX_WORKERS=10
  fi
  
  if [ "$CALC_CALLBACK_POOL_CONNECTIONS" -lt 5 ]; then
    CALC_CALLBACK_POOL_CONNECTIONS=5
  fi
  
  if [ "$CALC_CALLBACK_MAX_CONCURRENT" -lt 10 ]; then
    CALC_CALLBACK_MAX_CONCURRENT=10
  fi
  
  echo "回调服务参数计算:"
  echo "  - 每进程线程数: $CALC_CALLBACK_MAX_WORKERS"
  echo "  - 每进程连接池: $CALC_CALLBACK_POOL_CONNECTIONS"
  echo "  - 每进程最大并发: $CALC_CALLBACK_MAX_CONCURRENT"
  echo "  - 预估总线程数: $((CALC_CALLBACK_MAX_WORKERS * workers))"
  echo "  - 预估总连接数: $((CALC_CALLBACK_POOL_CONNECTIONS * workers))"
  echo "  - 预估总并发数: $((CALC_CALLBACK_MAX_CONCURRENT * workers))"
}

# 资源配置处理
process_resource_config() {
  echo "==================== 智能资源分配 ===================="
  
  # 处理工作进程数
  if [ "$WORKERS" = "auto" ]; then
    WORKERS=$(calculate_optimal_workers)
    echo "自动计算工作进程数: $WORKERS"
  else
    echo "手动指定工作进程数: $WORKERS"
    # 验证手动指定的进程数是否合理
    local optimal=$(calculate_optimal_workers)
    if [ "$WORKERS" -gt $((optimal * 2)) ]; then
      echo "警告: 指定的进程数($WORKERS)可能超出系统能力，建议值: $optimal"
    fi
  fi
  
  # 计算回调服务参数
  calculate_callback_params "$WORKERS"
  
  # 处理回调服务配置
  if [ "$CALLBACK_MAX_WORKERS" = "auto" ]; then
    CALLBACK_MAX_WORKERS=$CALC_CALLBACK_MAX_WORKERS
  fi
  
  if [ "$CALLBACK_POOL_CONNECTIONS" = "auto" ]; then
    CALLBACK_POOL_CONNECTIONS=$CALC_CALLBACK_POOL_CONNECTIONS
  fi
  
  if [ "$CALLBACK_POOL_MAXSIZE" = "auto" ]; then
    CALLBACK_POOL_MAXSIZE=$CALC_CALLBACK_POOL_CONNECTIONS
  fi
  
  if [ "$CALLBACK_MAX_CONCURRENT" = "auto" ]; then
    CALLBACK_MAX_CONCURRENT=$CALC_CALLBACK_MAX_CONCURRENT
  fi
  
  echo "=================================================="
}

# ==================== 工具函数 ====================

# 检测Python命令
detect_python() {
  if command -v python >/dev/null 2>&1; then
    PYTHON_CMD="python"
  elif command -v python3 >/dev/null 2>&1; then
    PYTHON_CMD="python3"
  else
    echo "错误: 系统中未找到 python 或 python3 命令"
    exit 1
  fi
  echo "使用Python命令: $PYTHON_CMD"
}

# 检查uvicorn是否安装
check_uvicorn() {
  if ! $PYTHON_CMD -c "import uvicorn" >/dev/null 2>&1; then
    echo "错误: 未安装uvicorn，请运行: pip install uvicorn"
    exit 1
  fi
}

# 清理缓存文件
clean_cache() {
  find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
  find . -name "*.pyc" -delete 2>/dev/null || true
  find . -name "*.pyo" -delete 2>/dev/null || true
}

# 设置环境变量
setup_environment() {
  # 基础环境变量
  export HOST=$HOST
  export PORT=$PORT
  
  # 先进行资源配置处理
  process_resource_config
  
  # 多进程相关环境变量
  if [ "$WORKERS" -gt 1 ]; then
    export WEB_CONCURRENCY=$WORKERS
    echo "多进程模式: $WORKERS 个工作进程"
  else
    echo "单进程模式"
  fi
  
  # 回调服务配置
  if [ -n "$CALLBACK_MAX_WORKERS" ] && [ "$CALLBACK_MAX_WORKERS" != "auto" ]; then
    export CALLBACK_MAX_WORKERS=$CALLBACK_MAX_WORKERS
    echo "回调服务线程数: $CALLBACK_MAX_WORKERS"
  fi
  
  if [ -n "$CALLBACK_POOL_CONNECTIONS" ] && [ "$CALLBACK_POOL_CONNECTIONS" != "auto" ]; then
    export CALLBACK_POOL_CONNECTIONS=$CALLBACK_POOL_CONNECTIONS
    echo "回调服务连接池: $CALLBACK_POOL_CONNECTIONS"
  fi
  
  if [ -n "$CALLBACK_POOL_MAXSIZE" ] && [ "$CALLBACK_POOL_MAXSIZE" != "auto" ]; then
    export CALLBACK_POOL_MAXSIZE=$CALLBACK_POOL_MAXSIZE
  fi
  
  if [ -n "$CALLBACK_MAX_CONCURRENT" ] && [ "$CALLBACK_MAX_CONCURRENT" != "auto" ]; then
    export CALLBACK_MAX_CONCURRENT=$CALLBACK_MAX_CONCURRENT
    echo "回调服务最大并发: $CALLBACK_MAX_CONCURRENT"
  fi
  
  # 生产环境配置
  if [ "$DEPLOYMENT_MODE" = "production" ] || [ "$ENV" = "production" ] || [ "$ENVIRONMENT" = "production" ]; then
    echo "生产环境模式：允许生成.pyc文件"
    export ENVIRONMENT="production"
  else
    echo "开发环境模式：禁用.pyc文件生成"
    export PYTHONDONTWRITEBYTECODE=1
    export ENVIRONMENT="development"
  fi
}

# 显示配置信息
show_config() {
  echo "==================== 最终启动配置 ===================="
  echo "应用模块: $APP_MODULE"
  echo "主机地址: $HOST"
  echo "端口号: $PORT"
  echo "工作进程数: $WORKERS"
  echo "部署模式: $DEPLOYMENT_MODE"
  echo "Python命令: $PYTHON_CMD"
  
  if [ "$WORKERS" -gt 1 ]; then
    echo "多进程配置:"
    echo "  - 总进程数: $WORKERS"
    echo "  - 每进程线程数: $CALLBACK_MAX_WORKERS"
    echo "  - 总线程数估算: $((CALLBACK_MAX_WORKERS * WORKERS))"
    echo "  - 每进程连接池: $CALLBACK_POOL_CONNECTIONS"
    echo "  - 总连接数估算: $((CALLBACK_POOL_CONNECTIONS * WORKERS))"
    echo "  - 每进程最大并发: $CALLBACK_MAX_CONCURRENT"
    echo "  - 总并发数估算: $((CALLBACK_MAX_CONCURRENT * WORKERS))"
  else
    echo "单进程配置:"
    echo "  - 线程数: $CALLBACK_MAX_WORKERS"
    echo "  - 连接池大小: $CALLBACK_POOL_CONNECTIONS"
    echo "  - 最大并发数: $CALLBACK_MAX_CONCURRENT"
  fi
  
  # 显示资源使用预估
  local cpu_cores=$(get_cpu_cores)
  local total_memory=$(get_total_memory)
  local estimated_memory=$((WORKERS * 300))  # 每个进程估算300MB
  
  echo "资源使用预估:"
  echo "  - CPU利用率: $((WORKERS * 100 / cpu_cores))%"
  echo "  - 内存使用: ${estimated_memory}MB / ${total_memory}MB"
  echo "  - 内存利用率: $((estimated_memory * 100 / total_memory))%"
  
  echo "=================================================="
}

# 检查端口是否被占用
check_port() {
  if lsof -i:$PORT >/dev/null 2>&1; then
    echo "警告: 端口 $PORT 已被占用"
    return 1
  fi
  return 0
}

# 强制释放端口
force_kill_port() {
  local pids=$(lsof -i:$PORT -t 2>/dev/null)
  if [ -n "$pids" ]; then
    echo "强制杀死进程: $pids"
    echo "$pids" | xargs kill -9 2>/dev/null
    sleep 2
    echo "端口 $PORT 已释放"
  fi
}

# 检查启动是否成功
check_startup_success() {
  echo "正在检查应用启动状态..."
  
  # 总等待时间：最多30秒
  local max_attempts=10
  local wait_interval=3
  local attempt=1
  
  while [ $attempt -le $max_attempts ]; do
    echo "  检查尝试 $attempt/$max_attempts (等待 ${wait_interval}s)..."
    sleep $wait_interval
    
    # 检查端口是否被监听
    if lsof -i:$PORT >/dev/null 2>&1; then
      echo "  ✓ 端口检测成功"
      
      # 额外等待2秒确保服务完全启动
      sleep 2
      
      # 尝试HTTP健康检查（如果可用）
      if command -v curl >/dev/null 2>&1; then
        if curl -s --connect-timeout 5 "http://$HOST:$PORT/health" >/dev/null 2>&1; then
          echo "  ✓ HTTP健康检查通过"
          return 0
        else
          echo "  ⚠ HTTP检查失败，但端口已监听"
          return 0  # 端口监听就认为启动成功
        fi
      else
        echo "  ✓ 端口已监听（无curl可用）"
        return 0
      fi
    fi
    
    echo "  ⏳ 端口尚未监听，继续等待..."
    attempt=$((attempt + 1))
  done
  
  echo "  ❌ 启动检测超时（${max_attempts}次尝试，总计$((max_attempts * wait_interval))秒）"
  return 1
}

# ==================== 启动函数 ====================

# 启动应用
start_app() {
  echo "正在启动 AI Novel Generator..."
  
  # 创建日志目录
  mkdir -p ./logs
  
  # 检测Python命令
  detect_python
  
  # 检测并激活虚拟环境
  if [ -d "venv" ]; then
    echo "检测到虚拟环境目录，正在激活..."
    source venv/bin/activate || { echo "激活虚拟环境失败"; return 1; }
    echo "虚拟环境已激活"
  else
    echo "未检测到虚拟环境目录，使用系统Python环境"
  fi
  
  # 检查uvicorn是否安装（在激活虚拟环境之后）
  check_uvicorn

  # 设置环境变量
  setup_environment
  
  # 显示配置信息
  show_config
  
  # 清理缓存文件
  clean_cache
  
  # 检查端口
  if ! check_port; then
    echo "是否强制释放端口 $PORT? (y/n)"
    read answer
    if [ "$answer" = "y" ]; then
      force_kill_port
    else
      echo "启动取消"
      return 1
    fi
  fi
  
  # 构建uvicorn启动命令
  UVICORN_CMD="$PYTHON_CMD -m uvicorn $APP_MODULE --host $HOST --port $PORT"
  
  # 根据工作进程数决定启动方式
  if [ "$WORKERS" -gt 1 ]; then
    UVICORN_CMD="$UVICORN_CMD --workers $WORKERS"
    echo "使用多进程模式启动: $WORKERS 个工作进程"
  else
    echo "使用单进程模式启动"
  fi
  
  # 开发环境添加热重载
  if [ "$DEPLOYMENT_MODE" != "production" ]; then
    UVICORN_CMD="$UVICORN_CMD --reload"
    echo "开发模式: 启用热重载"
  fi
  
  # 后台启动应用
  echo "执行启动命令: $UVICORN_CMD"
  $UVICORN_CMD > $LOG_FILE 2>&1 &
  
  # 检查启动是否成功
  if ! check_startup_success; then
    echo "⚠️ 启动检测超时，但这不一定意味着启动失败"
    echo ""
    echo "可能的原因和建议："
    echo "1. 系统资源较低，启动时间较长 - 请稍等片刻后检查状态"
    echo "2. 依赖安装问题 - 请检查日志: tail -f $LOG_FILE"
    echo "3. 端口冲突 - 请检查端口是否被占用"
    echo ""
    echo "检查命令："
    echo "  查看日志: tail -f $LOG_FILE"
    echo "  检查状态: bash scripts/run.sh status"
    echo "  检查端口: lsof -i:$PORT"
    echo ""
    
    # 给一点时间让进程完全启动
    echo "等待额外10秒后进行最终检查..."
    sleep 10
    
    # 最终检查一次
    if lsof -i:$PORT >/dev/null 2>&1; then
      echo "✅ 最终检查：应用已成功启动！"
      echo "主机地址: $HOST"
      echo "端口号: $PORT"
      echo "工作进程数: $WORKERS"
      echo "日志文件: $LOG_FILE"
      echo "API文档: http://$HOST:$PORT/docs"
      echo "Web客户端: http://$HOST:$PORT/web"
      echo "回调健康检查: http://$HOST:$PORT/api/v1/callbacks/health"
      echo ""
      echo "监控日志: tail -f $LOG_FILE"
      return 0
    else
      echo "❌ 最终检查：应用确实启动失败"
      echo "请检查日志文件获取详细错误信息: $LOG_FILE"
      return 1
    fi
  fi
  
  echo "✅ 应用启动成功！"
  echo "主机地址: $HOST"
  echo "端口号: $PORT"
  echo "工作进程数: $WORKERS"
  echo "日志文件: $LOG_FILE"
  echo "API文档: http://$HOST:$PORT/docs"
  echo "Web客户端: http://$HOST:$PORT/web"
  echo "回调健康检查: http://$HOST:$PORT/api/v1/callbacks/health"
  echo ""
  echo "监控日志: tail -f $LOG_FILE"
}

# 停止应用
stop_app() {
  local pids=$(lsof -i:$PORT -t 2>/dev/null)
  
  if [ -n "$pids" ]; then
    echo "正在停止应用 (PID: $pids)..."
    
    # 优雅关闭
    echo "$pids" | xargs kill 2>/dev/null
    sleep 2
    
    # 强制杀死剩余进程
    local remaining=$(lsof -i:$PORT -t 2>/dev/null)
    if [ -n "$remaining" ]; then
      echo "强制杀死进程: $remaining"
      echo "$remaining" | xargs kill -9 2>/dev/null
    fi
    
    echo "应用已停止"
  else
    echo "应用未运行"
  fi
}

# 重启应用
restart_app() {
  stop_app
  sleep 2
  start_app
}

# 显示应用状态
status_app() {
  local pids=$(lsof -i:$PORT -t 2>/dev/null)
  
  if [ -n "$pids" ]; then
    local pid_count=$(echo "$pids" | wc -w)
    
    echo "应用正在运行"
    echo "端口: $PORT"
    echo "进程数: $pid_count"
    
    # 显示进程信息
    for pid in $pids; do
      local uptime=$(ps -p $pid -o etime= 2>/dev/null | xargs)
      local memory=$(ps -p $pid -o rss= 2>/dev/null | xargs)
      local cpu=$(ps -p $pid -o %cpu= 2>/dev/null | xargs)
      echo "  PID: $pid, 运行时间: $uptime, CPU: ${cpu}%, 内存: $((memory/1024)) MB"
    done
    
    # 检查回调服务状态
    echo ""
    if command -v curl >/dev/null 2>&1; then
      echo "回调服务状态:"
      curl -s "http://$HOST:$PORT/api/v1/callbacks/health" | python -m json.tool 2>/dev/null || echo "回调服务检查失败"
    fi
  else
    echo "应用未运行"
  fi
}

# 监控应用
monitor_app() {
  echo "开始监控应用..."
  echo "按 Ctrl+C 退出监控"
  
  detect_python
  
  while true; do
    # 检查应用是否在运行
    if ! lsof -i:$PORT >/dev/null 2>&1; then
      echo "检测到应用已停止，正在重启..."
      start_app
    else
      # 显示状态信息
      local pids=$(lsof -i:$PORT -t 2>/dev/null)
      local pid_count=$(echo "$pids" | wc -w)
      local total_memory=0
      local total_cpu=0
      
      for pid in $pids; do
        local memory=$(ps -p $pid -o rss= 2>/dev/null | xargs)
        local cpu=$(ps -p $pid -o %cpu= 2>/dev/null | xargs)
        total_memory=$((total_memory + memory))
        total_cpu=$(echo "$total_cpu + $cpu" | bc 2>/dev/null || echo $total_cpu)
      done
      
      echo "$(date '+%Y-%m-%d %H:%M:%S') - 进程: $pid_count, CPU: ${total_cpu}%, 内存: $((total_memory/1024)) MB"
    fi
    
    sleep 10
  done
}

# 清理缓存命令
clean_app() {
  echo "清理应用缓存..."
  clean_cache
  echo "缓存清理完成"
}

# 显示帮助信息
show_help() {
  echo "AI Novel Generator 控制脚本 - 智能资源分配版"
  echo "用法: $0 [命令] [选项]"
  echo ""
  echo "命令:"
  echo "  start    启动应用"
  echo "  stop     停止应用"
  echo "  restart  重启应用"
  echo "  status   查看应用状态"
  echo "  monitor  监控应用并自动重启"
  echo "  clean    清理Python缓存文件"
  echo "  help     显示此帮助信息"
  echo ""
  echo "智能配置:"
  echo "  所有参数支持 'auto' 值，系统将根据CPU、内存、负载自动计算最优配置"
  echo ""
  echo "环境变量:"
  echo "  WORKERS=auto|4                    设置工作进程数（默认：auto）"
  echo "  DEPLOYMENT_MODE=production        设置部署模式（默认：development）"
  echo "  CALLBACK_MAX_WORKERS=auto|8       设置回调服务线程数（默认：auto）"
  echo "  CALLBACK_POOL_CONNECTIONS=auto|15 设置回调服务连接池大小（默认：auto）"
  echo "  CALLBACK_MAX_CONCURRENT=auto|25   设置回调服务最大并发数（默认：auto）"
  echo ""
  echo "智能分配算法:"
  echo "  • 工作进程数 = min(CPU核心数, 可用内存/300MB, 16)"
  echo "  • 生产环境更保守：进程数-1，内存/400MB"
  echo "  • 高负载时自动减半进程数"
  echo "  • 回调线程数 = CPU核心数 / 工作进程数"
  echo "  • 连接池和并发数按进程数等比例分配"
  echo ""
  echo "示例:"
  echo "  # 完全自动配置（推荐）"
  echo "  $0 start"
  echo ""
  echo "  # 自动配置生产环境"
  echo "  DEPLOYMENT_MODE=production $0 start"
  echo ""
  echo "  # 手动指定进程数，其他自动"
  echo "  WORKERS=4 $0 start"
  echo ""
  echo "  # 完全手动配置"
  echo "  WORKERS=4 CALLBACK_MAX_WORKERS=4 CALLBACK_MAX_CONCURRENT=20 $0 start"
  echo ""
  echo "  # 资源受限环境（手动调优）"
  echo "  WORKERS=2 CALLBACK_MAX_WORKERS=2 CALLBACK_MAX_CONCURRENT=10 $0 start"
  echo ""
  echo "系统要求:"
  echo "  • 最小内存: 1GB（单进程）"
  echo "  • 推荐内存: 2GB+（多进程）"
  echo "  • 推荐CPU: 2核心+"
}

# ==================== 主逻辑 ====================
case "${1:-start}" in
  start)
    start_app
    ;;
  stop)
    stop_app
    ;;
  restart)
    restart_app
    ;;
  status)
    status_app
    ;;
  monitor)
    monitor_app
    ;;
  clean)
    clean_app
    ;;
  help)
    show_help
    ;;
  *)
    echo "未知命令: $1"
    show_help
    exit 1
    ;;
esac

exit 0