## 核心冲突构建

### 1. 反派代理人
- **示例**：反派巫师派手下破坏主角探险队；敌对家族设局陷害主角
- **公式**：反派目标 + 阻碍主角行动
 
### 2. 命运筹码
- **示例**：任务失败将导致封印解除，恶魔重返人间；竞选失败则失去救母亲的医疗资源
- **公式**：失败后果 = 重要人/物/信念损失
 
### 3. 不可能任务
- **示例**：盲人剑客三天内穿越迷雾森林取回圣物；平民少年挑战贵族骑士团争取自治权
- **公式**：限定时间 + 看似无法达成的目标
 
## 角色困境设计
 
### 4. 两难抉择 & 致命秘密（合并）
- **示例**：选择揭发爱人的异端身份还是隐瞒真相；主角表面是医生实则是被通缉的杀手
- **公式**：A价值 vs B价值 / 隐藏身份 + 暴露风险
 
### 5. 实力/情感困境 & 身心创伤（合并）
- **示例**：恐水的水手必须潜入深海寻找解药；断腿的舞者强忍疼痛参加比赛
- **公式**：角色恐惧点/弱点 + 强制面对场景
 
## 情感与压迫
 
### 6. 情感三角 & 关系冲突（合并）
- **示例**：青梅竹马与志同道合战友同时陷入危机；导师与恋人分属敌对阵营
- **公式**：角色A + 角色B + 主角情感羁绊
 
### 7. 谎言误会 & 苦涩胜利（合并）
- **示例**：误信反派谎言误伤盟友；成功复仇却发现仇人是恩人
- **公式**：错误信息 + 冲突升级 / 目标达成 + 重大代价
 
## 时间与环境压力
 
### 8. 倒计时威胁 & 环境敌意（合并）
- **示例**：三天后月食时未摧毁诅咒项链将化为灰烬；沙漠探险遭遇沙暴+水源断绝+野兽追踪
- **公式**：时间限制 + 灾难性后果 / 恶劣环境 + 外部威胁
 
## 使用建议
选取2-3个核心维度组合，优先确保逻辑闭环（目标→阻碍→代价），通过具体场景强化角色张力。