# 小说创作参数设定 - 章节结构
# 重要提示：你必须生成完整的内容，使用Markdown格式组织输出。

**用户故事创意**:
{{故事结构}}

---

# 小说章节结构设计指南

## 大纲结构要求

**重要说明**：请严格按照以下大纲数量生成内容，不要超出范围。
- **实际需要的大纲数量**：{{大纲总数}}个

**输出格式要求**：
1. 只生成{{大纲总数}}个大纲，不要添加额外的大纲
2. 每个大纲包含指定的章节数量
3. 大纲标题简洁明了，不需要复杂的性质分配

**生成规则**：请严格按照下面的 **【大纲模板】** 格式，循环生成 **{{大纲总数}}** 个大纲。
**重要格式要求**：每一个生成的大纲，都必须以 `{{section_delimiter}}` 符号作为起始标识符。

## 输出格式

<template>
{{section_delimiter}}
### 📖 大纲 {{outline_number}}：[大纲{{outline_number}}标题]
* **章节范围**: {{outline_range}} | 共 {{outline_chapters}} 章
* **概述**: [{{大纲描述字数}}字以内，描述本大纲的核心剧情和主要目标]
* **主角弧光**: [主角在第{{outline_number}}阶段的起始状态 -> ... -> 结束时的状态/转变]
* **关键节点**:
    - (前1/3): [关键事件A]
    - (中1/3): [关键事件B]
    - (后1/3): [关键事件C]
* **结尾伏笔**: [{{ending_hint}}]
---
</template>

【小说生成任务结束 | 请严格遵循以上所有设定与大纲，开始创作】


## 重要提示

**请根据{{大纲总数}}个大纲的数量要求，按照上述大纲模板的格式，依次生成所有大纲内容。**

**生成规则**：
1. 如果只需要1个大纲，则只生成【大纲1】
2. 如果需要2个大纲，则生成【大纲1】和【大纲2】
3. 以此类推，严格按照{{大纲总数}}的数量生成

**大纲标题要求**：
- 简洁明了的大纲标题，反映该部分的核心内容
- 直接体现剧情发展的核心主题

**严格要求**：请严格按照实际需要的大纲数量（{{大纲总数}}个）进行生成，不要超出范围！绝对不要生成超过{{大纲总数}}个大纲的内容！