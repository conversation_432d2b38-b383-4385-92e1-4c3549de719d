# 小说细纲生成模板

## 用户创意
{{故事结构}}

## 基本信息
**大纲标题**: {{大纲标题}}
**章节范围**: {{章节范围}}

## 大纲规划
{{大纲内容}}

## 输出格式

**【重要规则】**
- **章节分隔符**: 每一章的细纲内容必须以独占一行的 `{{section_delimiter}}` 符号开始。这是一个绝对的、必须严格遵守的规则，绝不允许遗漏或更改。

【大纲概述】
### 1. 本大纲情节主线
- **核心任务/目标**：[主角在本阶段需要完成的核心使命或面临的最大挑战是什么？]
- **主要障碍与反派**：[反派或环境如何阻碍主角？关键的冲突事件有哪些？]
- **关键秘密/信息**：[本阶段揭露或涉及的最重要的秘密/线索是什么？]

### 2. 本大纲角色成长
- **主角困境与抉择**：[主角面临的最主要的内心挣扎、实力瓶颈或艰难选择是什么？]
- **关系变化**：[主角与关键角色（队友、敌人、导师）的关系发生了什么重要变化？]
- **期末状态**：[在本大纲结束时，主角在能力、心智、地位上达到了什么状态？]

### 3. 本大纲总结与展望
- **核心成就与转折**：[本大纲最重要的成果是什么？为整个故事带来了哪个关键性的转折？]
- **留下的伏笔**：[为下一个大纲或更远的未来，埋下了哪些具体的伏笔或悬念？]
- **涉及组织**：[简要列出本阶段活跃的主要组织及其作用。]

【大纲概述-结束】

<template>
{{section_delimiter}}

#### 【第{{chapter_number}}章】[标题]
- **功能**：[说明本章对主线/角色的核心作用。]
- **开端**：[开场场景，引入冲突/悬念。]
- **发展**：[角色的关键行动与情节推进。]
- **转折/高潮**：[矛盾爆发点与不可逆的事件。]
- **悬念**：[结尾的最后一幕，留下的钩子。]
- **要素**：
    - **角色**：[列出姓名]
    - **场景**：[列出地点]
    - **线索**：[列出关键物品/信息]
---
</template>

### 📌 前三章特别要求：
- **增加冲突和悬念**：在每一章的开头引入一个强烈的冲突或悬念，确保读者在第一时间就能感受到紧张感。
- **角色动机明确**：确保主要角色的动机在前三章中清晰可见，读者需要理解角色的目标和面临的障碍。
- **情感共鸣**：在情节中加入情感元素，让读者能够与角色产生共鸣，增加投入感。
- **引人入胜的开场**：每一章的开头可以使用引人入胜的场景或对话，迅速抓住读者的注意力。
- **设置悬念**：在每章结束时留下悬念，激发读者继续阅读的欲望。

### 📌 整体要求：
1. **逻辑连贯**：确保各章节之间的情节发展逻辑合理，承上启下
2. **场景清晰**：每章的场景设置要具体明确，包含确切的地点和环境描述
3. **机构组织**：合理安排各类机构组织的出现时机，确保与故事发展相匹配
4. **角色发展**：体现角色在本大纲中的成长变化和关系演进
5. **伏笔回收**：适当设置和回收伏笔，增强故事的完整性
6. **悬念维持**：每章都要有吸引读者继续阅读的元素