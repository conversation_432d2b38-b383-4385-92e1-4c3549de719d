# LLM 推理服务 Web 客户端

这是一个基于原生WebSocket的Web客户端，用于连接和使用LLM推理服务。

## 功能特点

- 原生WebSocket连接，无需额外依赖
- 支持单轮和多轮对话
- 实时流式显示推理结果
- 任务状态管理和重连功能
- 友好的聊天界面
- 实时token使用统计

## 快速开始

### 方法一：使用Node.js HTTP服务器（推荐）

1. 确保已安装Node.js (版本 >= 14.0.0)

2. 进入客户端目录：
   ```bash
   cd client
   ```

3. 启动HTTP服务器：
   ```bash
   npm start
   ```
   
   或者指定其他端口：
   ```bash
   CLIENT_PORT=3001 npm start
   ```

4. 打开浏览器访问：
   ```
   http://localhost:3000
   ```

### 方法二：直接打开HTML文件

直接用浏览器打开 `index.html` 文件：
```
file:///path/to/client/index.html
```

## 使用说明

### 连接服务器

1. 在页面底部的服务器配置区域中输入：
   - 主机地址（默认：localhost）
   - 端口号（默认：8080）

2. 点击"连接服务器"按钮

3. 连接成功后，状态指示器会变为绿色

### 开始对话

1. 点击"新对话"按钮开始新的多轮对话
2. 在输入框中输入消息
3. 按回车键或点击"发送"按钮
4. 观察AI的流式回复和token统计

### 连接现有任务

1. 在任务控制区域输入任务ID
2. 点击"连接任务"按钮
3. 系统会加载该任务的历史内容

## 配置选项

### 环境变量

- `CLIENT_PORT`: HTTP服务器端口（默认：3000）
- `CLIENT_HOST`: HTTP服务器主机（默认：localhost）

### 使用示例

```bash
# 在端口3001上启动服务器
CLIENT_PORT=3001 npm start

# 在所有网络接口上启动服务器
CLIENT_HOST=0.0.0.0 CLIENT_PORT=3000 npm start
```

## 开发说明

### 文件结构

```
client/
├── index.html          # 主页面文件
├── server.js           # Node.js HTTP服务器
├── package.json        # Node.js项目配置
└── README.md          # 此文件
```

### 服务器特性

- 支持静态文件服务
- 自动MIME类型检测
- CORS支持
- 友好的错误页面
- 优雅关闭机制
- 彩色日志输出

### 故障排除

1. **端口被占用**：
   ```bash
   CLIENT_PORT=3001 npm start
   ```

2. **无法连接WebSocket服务器**：
   - 确保LLM推理服务器正在运行
   - 检查服务器地址和端口配置
   - 查看浏览器控制台错误信息

3. **页面无法加载**：
   - 确保在正确的目录中运行 `npm start`
   - 检查Node.js版本是否 >= 14.0.0

## 注意事项

- 确保LLM推理服务器在使用客户端之前已启动
- 建议使用现代浏览器（Chrome、Firefox、Safari、Edge）
- 如果遇到CORS错误，请使用Node.js HTTP服务器而不是直接打开HTML文件 