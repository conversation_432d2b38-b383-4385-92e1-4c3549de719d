<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LLM推理服务客户端</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .chat-container {
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
            background-color: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .chat-header {
            background-color: #4CAF50;
            color: white;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .status {
            display: flex;
            align-items: center;
            font-size: 14px;
        }
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .connected {
            background-color: green;
        }
        .disconnected {
            background-color: red;
        }
        .chat-area {
            height: 400px;
            padding: 15px;
            overflow-y: auto;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 10px;
            max-width: 80%;
            word-wrap: break-word;
        }
        .user {
            background-color: #E3F2FD;
            margin-left: auto;
        }
        .assistant {
            background-color: #F5F5F5;
            margin-right: auto;
        }
        .system-message {
            text-align: center;
            color: #757575;
            margin: 10px 0;
            font-size: 12px;
        }
        .input-area {
            display: flex;
            padding: 15px;
            background-color: #f5f5f5;
            border-top: 1px solid #ddd;
        }
        #messageInput {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        #sendButton {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            margin-left: 10px;
            cursor: pointer;
        }
        #sendButton:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        pre {
            white-space: pre-wrap;
            background-color: #f0f0f0;
            padding: 5px;
            border-radius: 3px;
        }
        code {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            background-color: #f0f0f0;
            padding: 2px 4px;
            border-radius: 3px;
        }
        #taskIdDisplay {
            color: #555;
            font-size: 12px;
            margin: 10px 0;
            text-align: center;
        }
        .ping-button {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }
        .task-controls {
            display: flex;
            justify-content: center;
            padding: 10px;
            border-top: 1px solid #ddd;
            background-color: #f9f9f9;
        }
        #taskIdInput {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 5px;
            width: 150px;
        }
        #connectTaskButton {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
        }
        #connectTaskButton:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .task-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 15px;
        }
        
        .server-config {
            display: flex;
            justify-content: center;
            padding: 10px;
            border-top: 1px solid #ddd;
            background-color: #f0f0f0;
        }
        
        #serverHost, #serverPort {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 5px;
        }
        
        #serverHost {
            width: 150px;
        }
        
        #serverPort {
            width: 100px;
        }
        
        #connectServerButton {
            background-color: #673AB7;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
        }
        #newChatButton {
            background-color: #FF5722;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }
        .system-config {
            padding: 10px 15px;
            border-top: 1px solid #ddd;
            background-color: #f9f9f9;
        }
        .system-config label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
            color: #666;
            font-weight: bold;
        }
        #systemMessageInput {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
            font-size: 14px;
            font-family: Arial, sans-serif;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h2>LLM推理服务客户端</h2>
            <div class="status">
                <div id="connectionStatus" class="status-indicator disconnected"></div>
                <span>连接状态</span>
            </div>
        </div>
        <div id="chatArea" class="chat-area"></div>
        <div id="tokenStatsArea" style="padding: 5px 10px; border-top: 1px solid #ddd; background-color: #f8f8f8; color: #666; font-size: 12px;">
            提示词tokens: 0 | 完成词tokens: 0 | 总tokens: 0
        </div>
        <div class="input-area">
            <input type="text" id="messageInput" placeholder="输入消息..." disabled>
            <button id="sendButton" disabled>发送</button>
        </div>
        <div class="task-info">
            <div id="taskIdDisplay">当前任务ID: 无</div>
            <button id="newChatButton" disabled>新对话</button>
        </div>
        <div class="system-config">
            <label for="systemMessageInput">System消息设置（AI角色定义）：</label>
            <textarea id="systemMessageInput" placeholder="输入system消息来定义AI的角色和行为准则..." rows="2">你是一个有用的AI助手。</textarea>
        </div>
        <div class="task-controls">
            <input type="text" id="taskIdInput" placeholder="输入任务ID来连接..." disabled>
            <button id="connectTaskButton" disabled>连接任务</button>
            <button id="endpointStatsButton" disabled style="background-color: #9C27B0; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; margin-left: 5px;">端点统计</button>
        </div>
        <div class="server-config">
            <input type="text" id="serverHost" placeholder="主机 (默认: **************)" value="**************">
            <input type="text" id="serverPort" placeholder="端口 (默认: 3000)" value="3000">
            <button id="connectServerButton">连接服务器</button>
        </div>
    </div>

    <!-- 不再需要Socket.IO库，使用原生WebSocket -->
    <script>
        // 初始化WebSocket连接参数
        let HOST = localStorage.getItem('serverHost') || window.location.hostname || '**************';
        let PORT = localStorage.getItem('serverPort') || 3000;
        let socket;
        let isConnected = false;
        let socketId = null;
        let isWaitingForResponse = false;
        let currentTaskId = null;
        
        // 存储对话历史 (OpenAI格式)
        let messageHistory = [];
        
        // 页面加载完成后初始化
        window.addEventListener('DOMContentLoaded', () => {
            initializeUI();
            
            // 从本地存储加载之前保存的服务器配置
            const savedHost = localStorage.getItem('serverHost');
            const savedPort = localStorage.getItem('serverPort');
            
            if (savedHost) document.getElementById('serverHost').value = savedHost;
            if (savedPort) document.getElementById('serverPort').value = savedPort;
            
            // 不自动连接，等待用户点击连接按钮
        });
        
        // DOM元素
        const chatArea = document.getElementById('chatArea');
        const connectionStatus = document.getElementById('connectionStatus');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const taskIdDisplay = document.getElementById('taskIdDisplay');
        const taskIdInput = document.getElementById('taskIdInput');
        const connectTaskButton = document.getElementById('connectTaskButton');
        const newChatButton = document.getElementById('newChatButton');
        
        // 初始化界面
        function initializeUI() {
            // 设置新对话按钮事件
            newChatButton.addEventListener('click', () => {
                console.log('点击新对话按钮');
                
                // 清除当前任务状态
                currentTaskId = null;
                taskIdDisplay.textContent = '当前任务ID: 无';
                
                // 清除聊天区域
                chatArea.innerHTML = '';
                addSystemMessage('开始新对话，请在输入框中输入内容...');
                
                // 清除消息历史
                messageHistory = [];
                console.log('清除消息历史');
                
                // 获取用户设置的system消息
                const systemMessageInput = document.getElementById('systemMessageInput');
                const systemContent = systemMessageInput.value.trim() || '你是一个有用的AI助手。';
                console.log('获取system消息:', systemContent);
                
                // 添加系统消息作为对话的起点
                const systemMessage = {
                    role: 'system',
                    content: systemContent
                };
                messageHistory.push(systemMessage);
                
                console.log('新对话开始，system消息已添加到历史');
                console.log('新对话后的消息历史:', JSON.stringify(messageHistory, null, 2));
                
                // 确保界面可用
                isWaitingForResponse = false;
                enableInterface();
            });
            
            // 设置连接服务器按钮事件
            document.getElementById('connectServerButton').addEventListener('click', () => {
                const hostInput = document.getElementById('serverHost');
                const portInput = document.getElementById('serverPort');
                
                HOST = hostInput.value.trim() || '**************';
                PORT = portInput.value.trim() || '3000';
                
                // 保存到本地存储
                localStorage.setItem('serverHost', HOST);
                localStorage.setItem('serverPort', PORT);
                
                // 断开现有连接
                if (socket && socket.readyState === WebSocket.OPEN) {
                    socket.close();
                }
                
                // 清除聊天区域
                chatArea.innerHTML = '';
                addSystemMessage(`正在连接到服务器: ${HOST}:${PORT}...`);
                
                // 连接新服务器
                connectWebSocket();
            });
            
            // 设置端点统计按钮事件
            document.getElementById('endpointStatsButton').addEventListener('click', () => {
                requestEndpointStats();
            });
        }
        
        // 连接WebSocket
        function connectWebSocket() {
            // 创建原生WebSocket连接
            const wsUrl = `ws://${HOST}:${PORT}/ws`;
            console.log(`尝试连接到WebSocket服务器: ${wsUrl}`);
            socket = new WebSocket(wsUrl);

            // 连接成功
            socket.onopen = function() {
                isConnected = true;
                connectionStatus.classList.remove('disconnected');
                connectionStatus.classList.add('connected');
                console.log('已连接到WebSocket服务器');
                
                // 测试按钮直接启用
                newChatButton.disabled = false;
                addSystemMessage('连接成功！请点击"新对话"按钮开始聊天。');
            };
            
            // 连接断开
            socket.onclose = function(event) {
                isConnected = false;
                socketId = null;
                connectionStatus.classList.remove('connected');
                connectionStatus.classList.add('disconnected');
                disableInterface();
                console.log('与服务器断开连接', event.code, event.reason);
                addSystemMessage(`与服务器断开连接: ${event.reason || '未知原因'}`);
            };

            // 连接错误
            socket.onerror = function(error) {
                console.error('WebSocket错误:', error);
                addSystemMessage('连接错误，请检查服务器是否运行');
            };

            // 接收来自服务器的消息
            socket.onmessage = function(event) {
                console.log('从服务器收到消息:', event.data);
                const data = JSON.parse(event.data);
                
                switch (data.type) {
                    case 'connected':
                        console.log('收到连接确认消息，socketId =', data.socketId);
                        socketId = data.socketId;  // 保存服务器分配的ID
                        // 在收到连接确认后启用界面
                        isWaitingForResponse = false;
                        enableInterface();
                        break;
                        
                    case 'token':
                        // 处理流式生成的token
                        appendToken(data.content);
                        
                        // 更新token统计
                        updateTokenStats(
                            data.promptTokens || 0,
                            data.completionTokens || 0,
                            data.totalTokens || 0
                        );
                        break;
                        
                    case 'complete':
                        // 处理推理完成事件
                        isWaitingForResponse = false;
                        enableInterface();
                        
                        // 如果任务ID是新的，更新显示
                        if (currentTaskId !== data.taskId) {
                            currentTaskId = data.taskId;
                            taskIdDisplay.textContent = `当前任务ID: ${currentTaskId}`;
                        }
                        
                        // 如果有错误消息，显示给用户
                        if (data.error) {
                            addSystemMessage(`错误: ${data.error}`);
                        }
                        
                        break;
                        
                    case 'taskCreated':
                        currentTaskId = data.taskId;
                        taskIdDisplay.textContent = `当前任务ID: ${currentTaskId}`;
                        addSystemMessage(`新对话已创建, 任务ID: ${currentTaskId}`);
                        enableInterface();
                        break;
                    
                    case 'error':
                        addSystemMessage(`错误: ${data.message}`);
                        isWaitingForResponse = false;
                        enableInterface();
                        break;
                    
                    case 'done':
                        // 获取完整的AI回复内容
                        const doneResponseElement = document.getElementById(currentResponseId);
                        if (doneResponseElement) {
                            // 获取纯文本内容（移除HTML标签）
                            const assistantContent = doneResponseElement.innerText || doneResponseElement.textContent || '';
                            
                            // 将助手回复添加到历史记录中
                            if (assistantContent.trim()) {
                                const assistantMessage = {
                                    role: 'assistant',
                                    content: assistantContent.trim()
                                };
                                messageHistory.push(assistantMessage);
                                console.log('AI回复已添加到历史:', assistantContent.trim());
                            }
                        }
                        
                        // 显示token使用统计
                        const statsMsg = `推理完成\n提示词令牌: ${data.promptTokens} - 生成令牌: ${data.completionTokens} - 总令牌: ${data.totalTokens}`;
                        addSystemMessage(statsMsg);
                        
                        // 更新token统计显示
                        updateTokenStats(
                            data.promptTokens || 0,
                            data.completionTokens || 0,
                            data.totalTokens || 0
                        );
                        
                        isWaitingForResponse = false;
                        enableInterface();
                        
                        // 输出当前消息历史，便于调试
                        console.log('当前消息历史:', JSON.stringify(messageHistory, null, 2));
                        break;
                        
                    case 'subscribed':
                        addSystemMessage(`已连接到任务: ${data.taskId}`);
                        currentTaskId = data.taskId;
                        taskIdDisplay.textContent = `当前任务ID: ${currentTaskId}`;
                        enableInterface();
                        break;
                        
                    case 'history':
                        // 创建一个新的唯一ID用于历史内容
                        responseCounter++;
                        currentResponseId = 'assistantResponse-history-' + responseCounter;
                        
                        // 添加一个新的助手消息框来显示历史内容
                        const historyMessage = addCurrentAssistantMessage(currentResponseId);
                        
                        // 显示历史内容（处理换行符）
                        let historyContent = data.content;
                        historyContent = historyContent.replace(/\\n/g, '{{NEWLINE}}');
                        historyContent = historyContent.replace(/\n/g, '{{NEWLINE}}');
                        historyContent = historyContent.replace(/{{NEWLINE}}/g, '<br>');
                        historyMessage.innerHTML = historyContent;
                        
                        addSystemMessage(`已加载任务历史内容`);
                        scrollToBottom();
                        break;
                        
                    case 'endpointStats':
                        displayEndpointStats(data.stats, data.availableEndpoints);
                        break;
                }
            };
        }
        
        // 启用界面
        function enableInterface() {
            if (isConnected) {
                newChatButton.disabled = false;
                taskIdInput.disabled = false;
                connectTaskButton.disabled = false;
                document.getElementById('endpointStatsButton').disabled = false;
                
                // 如果不在等待响应中，则启用输入框和发送按钮
                // 不再需要存在currentTaskId，因为第一次发送时会自动创建任务
                if (!isWaitingForResponse) {
                    messageInput.disabled = false;
                    sendButton.disabled = false;
                }
            }
        }
        
        // 禁用界面
        function disableInterface() {
            messageInput.disabled = true;
            sendButton.disabled = true;
            newChatButton.disabled = true;
            taskIdInput.disabled = true;
            connectTaskButton.disabled = true;
            document.getElementById('endpointStatsButton').disabled = true;
        }
        
        // 发送消息
        function sendMessage(data) {
            if (socket && socket.readyState === WebSocket.OPEN) {
                console.log('发送消息到服务器:', data);
                socket.send(JSON.stringify(data));
            } else {
                console.error('消息发送失败: 未连接到服务器');
                addSystemMessage('消息发送失败: 未连接到服务器');
            }
        }
        
        // 我们用一个计数器为每个助手回复创建唯一ID
        let responseCounter = 0;
        let currentResponseId = null;

        // 发送推理请求 (多轮对话版本)
        function sendInferenceRequest(message) {
            // 添加用户消息到聊天区域
            addUserMessage(message);
            
            // 每次新的对话创建一个新的唯一ID
            responseCounter++;
            currentResponseId = 'assistantResponse-' + responseCounter;
            
            // 添加新的助手答复框
            addCurrentAssistantMessage(currentResponseId);
            
            // 将用户消息添加到历史
            const userMessage = {
                role: 'user',
                content: message
            };
            messageHistory.push(userMessage);
            
            // 输出调试信息
            console.log('发送推理请求前的消息历史:');
            console.log('消息历史数组长度:', messageHistory.length);
            messageHistory.forEach((msg, index) => {
                console.log(`消息${index + 1}: ${msg.role} -> ${msg.content}`);
            });
            
            // 发送消息到服务器（使用完整的消息历史）
            const requestData = {
                action: 'inferWithMessages',
                messages: JSON.stringify(messageHistory)
            };
            
            console.log('发送的请求数据:', requestData);
            sendMessage(requestData);
            
            // 设置等待状态
            isWaitingForResponse = true;
            disableInterface();
        }
        
        // 添加系统消息
        function addSystemMessage(message) {
            const systemMessage = document.createElement('div');
            systemMessage.className = 'system-message';
            systemMessage.textContent = message;
            chatArea.appendChild(systemMessage);
            scrollToBottom();
        }
        
        // 添加用户消息
        function addUserMessage(message) {
            const userMessage = document.createElement('div');
            userMessage.className = 'message user';
            userMessage.textContent = message;
            chatArea.appendChild(userMessage);
            scrollToBottom();
        }
        
        // 创建当前助手回复框
        function addCurrentAssistantMessage(responseId) {
            const assistantMessage = document.createElement('div');
            assistantMessage.className = 'message assistant';
            assistantMessage.id = responseId || 'currentResponse';
            chatArea.appendChild(assistantMessage);
            scrollToBottom();
            return assistantMessage;
        }
        
        // 添加token到当前回复
        function appendToken(token) {
            // 使用当前还在生成的回复框ID
            const responseElement = document.getElementById(currentResponseId || 'currentResponse');
            
            if (responseElement) {
                // 检查token是否包含换行符，包括\n和实际的换行符
                // 先保存当前文本
                const currentText = responseElement.innerHTML || responseElement.textContent;
                
                // 创建一个带有token的新内容
                let newContent = currentText + token;
                
                // 先将字符\n替换为特殊标记
                newContent = newContent.replace(/\\n/g, '{{NEWLINE}}');
                
                // 再将实际的换行符替换为特殊标记
                newContent = newContent.replace(/\n/g, '{{NEWLINE}}');
                
                // 最后将所有特殊标记替换为HTML换行标签
                newContent = newContent.replace(/{{NEWLINE}}/g, '<br>');
                
                // 使用innerHTML更新内容
                responseElement.innerHTML = newContent;
                
                scrollToBottom();
            } else {
                // 如果没有当前回复元素，创建一个
                addCurrentAssistantMessage(currentResponseId);
                appendToken(token);
            }
        }
        
        // 滚动到聊天区域底部
        function scrollToBottom() {
            chatArea.scrollTop = chatArea.scrollHeight;
        }
        
        // 更新token统计信息
        function updateTokenStats(promptTokens, completionTokens, totalTokens) {
            const tokenStatsArea = document.getElementById('tokenStatsArea');
            if (tokenStatsArea) {
                tokenStatsArea.innerHTML = `提示词tokens: ${promptTokens} | 完成词tokens: ${completionTokens} | 总tokens: ${totalTokens}`;
            }
        }
        
        // 发送按钮点击事件
        sendButton.addEventListener('click', () => {
            const message = messageInput.value.trim();
            if (message) {
                // 检查是否需要初始化消息历史
                if (messageHistory.length === 0) {
                    console.log('消息历史为空，自动初始化system消息');
                    
                    // 获取用户设置的system消息
                    const systemMessageInput = document.getElementById('systemMessageInput');
                    const systemContent = systemMessageInput.value.trim() || '你是一个有用的AI助手。';
                    
                    // 添加系统消息
                    const systemMessage = {
                        role: 'system',
                        content: systemContent
                    };
                    messageHistory.push(systemMessage);
                    
                    console.log('自动添加system消息:', systemContent);
                }
                
                sendInferenceRequest(message);
                messageInput.value = '';
            }
        });
        
        // 输入框按回车发送
        messageInput.addEventListener('keyup', (event) => {
            if (event.key === 'Enter' && !event.shiftKey) {
                sendButton.click();
                event.preventDefault();
            }
        });
        
        // 连接任务按钮事件
        connectTaskButton.addEventListener('click', () => {
            const taskId = taskIdInput.value.trim();
            if (taskId) {
                // 清除聊天区域
                chatArea.innerHTML = '';
                addSystemMessage(`正在连接到任务: ${taskId}...`);
                
                // 清除消息历史，连接现有任务时重新开始对话上下文
                messageHistory = [];
                
                // 获取用户设置的system消息，为新的对话上下文做准备
                const systemMessageInput = document.getElementById('systemMessageInput');
                const systemContent = systemMessageInput.value.trim() || '你是一个有用的AI助手。';
                
                // 添加系统消息
                const systemMessage = {
                    role: 'system',
                    content: systemContent
                };
                messageHistory.push(systemMessage);
                
                console.log('连接任务，重置消息历史，system消息:', systemContent);
                
                // 发送订阅请求
                sendMessage({
                    action: 'subscribeTask',
                    taskId: taskId
                });
                
                // 更新当前任务ID
                currentTaskId = taskId;
                taskIdDisplay.textContent = `当前任务ID: ${currentTaskId}`;
                
                // 重置等待状态
                isWaitingForResponse = false;
                enableInterface();
            }
        });
        
        // ========== 端点统计相关函数 ==========
        
        /**
         * 请求推理端点统计信息
         */
        function requestEndpointStats() {
            if (!isConnected) {
                addSystemMessage('请先连接到服务器');
                return;
            }
            
            sendMessage({
                action: 'getEndpointStats'
            });
        }
        
        /**
         * 显示推理端点统计信息
         */
        function displayEndpointStats(stats, availableEndpoints) {
            const statsMessage = `
=== 推理端点统计信息 ===
可用端点数: ${availableEndpoints}

${stats}

📝 推理参数说明:
• max_new_tokens: 最大新生成token数 [0-12288]
• temperature: 温度参数 [0-1]，值越高输出越随机
• top_p: 核采样参数 [0-1]，控制输出多样性
• top_k: 选择概率最高的k个token，影响创造性
            `;
            
            addSystemMessage(statsMessage);
        }
    </script>
</body>
</html>
