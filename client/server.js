const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

// 配置参数
const PORT = process.env.CLIENT_PORT || 3000;
const HOST = process.env.CLIENT_HOST || 'localhost';

// MIME类型映射
const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpg',
    '.gif': 'image/gif',
    '.ico': 'image/x-icon',
    '.svg': 'image/svg+xml'
};

// 创建HTTP服务器
const server = http.createServer((req, res) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
    
    // 解析URL
    const parsedUrl = url.parse(req.url);
    let pathname = parsedUrl.pathname;
    
    // 默认页面重定向到index.html
    if (pathname === '/') {
        pathname = '/index.html';
    }
    
    // 构建文件路径
    const filePath = path.join(__dirname, pathname);
    
    // 检查文件是否存在
    fs.access(filePath, fs.constants.F_OK, (err) => {
        if (err) {
            // 文件不存在，返回404
            res.writeHead(404, { 'Content-Type': 'text/html' });
            res.end(`
                <html>
                    <head><title>404 Not Found</title></head>
                    <body>
                        <h1>404 - File Not Found</h1>
                        <p>The requested file <code>${pathname}</code> was not found.</p>
                        <p><a href="/">返回首页</a></p>
                    </body>
                </html>
            `);
            return;
        }
        
        // 获取文件扩展名
        const ext = path.extname(filePath).toLowerCase();
        const contentType = mimeTypes[ext] || 'application/octet-stream';
        
        // 读取并返回文件
        fs.readFile(filePath, (err, data) => {
            if (err) {
                res.writeHead(500, { 'Content-Type': 'text/html' });
                res.end(`
                    <html>
                        <head><title>500 Internal Server Error</title></head>
                        <body>
                            <h1>500 - Internal Server Error</h1>
                            <p>Unable to read file: ${err.message}</p>
                        </body>
                    </html>
                `);
                return;
            }
            
            // 设置CORS头，允许跨域访问
            res.writeHead(200, {
                'Content-Type': contentType,
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            });
            res.end(data);
        });
    });
});

// 错误处理
server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.error(`\x1b[31m错误: 端口 ${PORT} 已被占用\x1b[0m`);
        console.log(`请尝试使用其他端口: CLIENT_PORT=3001 npm start`);
    } else {
        console.error('服务器错误:', err);
    }
    process.exit(1);
});

// 优雅关闭
process.on('SIGTERM', () => {
    console.log('\n收到SIGTERM信号，正在关闭服务器...');
    server.close(() => {
        console.log('HTTP服务器已关闭');
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('\n收到SIGINT信号，正在关闭服务器...');
    server.close(() => {
        console.log('HTTP服务器已关闭');
        process.exit(0);
    });
});

// 启动服务器
server.listen(PORT, HOST, () => {
    console.log('\x1b[36m%s\x1b[0m', '='.repeat(60));
    console.log('\x1b[32m%s\x1b[0m', '🚀 LLM推理服务Web客户端已启动');
    console.log('\x1b[36m%s\x1b[0m', '='.repeat(60));
    console.log(`📍 服务地址: \x1b[33mhttp://${HOST}:${PORT}\x1b[0m`);
    console.log(`📁 服务目录: \x1b[90m${__dirname}\x1b[0m`);
    console.log(`🌐 主页面: \x1b[33mhttp://${HOST}:${PORT}/index.html\x1b[0m`);
    console.log('\x1b[36m%s\x1b[0m', '='.repeat(60));
    console.log('💡 使用 Ctrl+C 停止服务器');
    console.log();
}); 