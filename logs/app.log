INFO:     Will watch for changes in these directories: ['/Volumes/990PRO/dev/python/ai-novel']
WARNING:  "workers" flag is ignored when reloading is enabled.
INFO:     Uvicorn running on http://0.0.0.0:3300 (Press CTRL+C to quit)
INFO:     Started reloader process [12842] using WatchFiles
2025-06-23 13:50:17,385 - uvicorn.error - INFO - Started server process [12849]
2025-06-23 13:50:17,385 - uvicorn.error - INFO - Waiting for application startup.
2025-06-23 13:50:17,385 - novel-api - INFO - === 小说生成API服务启动 ===
2025-06-23 13:50:17,385 - novel-api - INFO - 正在创建数据库表...
2025-06-23 13:50:18,223 - app.config.database - INFO - Database tables created successfully
2025-06-23 13:50:18,224 - novel-api - INFO - 数据库表创建完成
2025-06-23 13:50:18,224 - novel-api - INFO - 正在设置用户追踪监听器...
2025-06-23 13:50:18,225 - novel-api - INFO - 用户追踪监听器设置完成
2025-06-23 13:50:18,225 - novel-api - INFO - 小说生成API服务启动完成
2025-06-23 13:50:18,226 - uvicorn.error - INFO - Application startup complete.
2025-06-23 13:50:20,085 - root - INFO - 雪花算法ID池初始化完成，当前池大小: 5000, 节点ID: 10
2025-06-23 13:50:21,766 - uvicorn.access - INFO - 127.0.0.1:57425 - "GET /health HTTP/1.1" 200
