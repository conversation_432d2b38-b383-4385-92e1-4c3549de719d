from typing import Dict, Any, Optional, List
import logging
import yaml
import os
import json
from sqlalchemy.orm import Session
from datetime import datetime

from app.services.novel_service import NovelService
from app.models.novel import OutlineForkDB, StoryStructureDB, OutlineDB, ChapterStructureForkDB, ChapterContentDB
from app.utils.snowflake import get_next_id
from app.utils.inference_websocket import InferenceManager
from app.config.configuration import config

logger = logging.getLogger(__name__)


class ChapterContentService:
    """章节内容服务 - 基于大纲分支生成章节内容"""
    
    def __init__(self, db: Session):
        self.db = db
        self.novel_service = NovelService(db)
        self._app_config = None  # 缓存应用配置
    
    def _load_app_config(self) -> Dict[str, Any]:
        """加载应用配置文件"""
        if self._app_config is None:
            try:
                config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'application.yaml')
                with open(config_path, 'r', encoding='utf-8') as file:
                    self._app_config = yaml.safe_load(file)
                logger.info("章节内容服务 - 应用配置加载成功")
            except Exception as e:
                logger.error(f"加载应用配置失败: {e}")
                self._app_config = {}
        return self._app_config
    
    def _load_template(self, template_name: str) -> str:
        """加载指定的模板文件"""
        try:
            template_path = os.path.join(os.path.dirname(__file__), '..', '..', 'prompts', template_name)
            with open(template_path, 'r', encoding='utf-8') as file:
                return file.read()
        except Exception as e:
            logger.error(f"加载模板失败 {template_name}: {e}")
            raise ValueError(f"无法加载模板 {template_name}: {e}")
    
    def _apply_template_replacements(self, template: str, replacements: Dict[str, str]) -> str:
        """执行模板替换"""
        result = template
        for key, value in replacements.items():
            # 确保value是字符串，如果是None则转为空字符串
            str_value = "" if value is None else str(value)
            result = result.replace(f"{{{{{key}}}}}", str_value)
        return result
    
    def generate_chapter_content(self, novel_id: int, outline_fork_id: int, 
                               callback_url: Optional[str] = None) -> Dict[str, Any]:
        """基于大纲分支生成章节内容"""
        try:
            # 1. 验证小说存在
            novel = self.novel_service.get_novel_by_id(novel_id)
            if not novel:
                raise ValueError(f"小说 {novel_id} 不存在")
            
            # 2. 验证大纲分支存在且已完成
            outline_fork = self.db.query(OutlineForkDB).filter(
                OutlineForkDB.id == outline_fork_id
            ).first()
            
            if not outline_fork:
                raise ValueError(f"大纲分支 {outline_fork_id} 不存在")
            
            if outline_fork.novel_id != novel_id:
                raise ValueError(f"大纲分支 {outline_fork_id} 不属于小说 {novel_id}")
            
            if outline_fork.fork_status != "completed":
                raise ValueError(f"大纲分支 {outline_fork_id} 尚未完成，无法生成章节内容")
            
            if not outline_fork.outline_inference:
                raise ValueError(f"大纲分支 {outline_fork_id} 没有推理结果，无法生成章节内容")
            
            # 3. 检查是否已存在该章节内容任务
            existing_chapter = self.db.query(ChapterContentDB).filter(
                ChapterContentDB.novel_id == novel_id,
                ChapterContentDB.outline_fork_id == outline_fork_id
            ).first()
            
            if existing_chapter:
                if existing_chapter.status in ["pending", "running"]:
                    raise ValueError(f"章节内容生成任务已存在且正在进行中 (ID: {existing_chapter.id})")
                elif existing_chapter.status == "completed":
                    raise ValueError(f"章节内容已生成完成 (ID: {existing_chapter.id})")
            
            # 4. 获取大纲任务信息
            outline_task = self.db.query(OutlineDB).filter(
                OutlineDB.id == outline_fork.outline_task_id
            ).first()
            
            if not outline_task:
                raise ValueError(f"找不到大纲分支 {outline_fork_id} 对应的大纲任务")
            
            # 5. 获取章节结构分支信息
            chapter_structure_fork = self.db.query(ChapterStructureForkDB).filter(
                ChapterStructureForkDB.id == outline_task.chapter_structure_fork_id
            ).first()
            
            if not chapter_structure_fork or not chapter_structure_fork.chapter_structures_inference:
                raise ValueError(f"找不到对应的章节结构分支或章节结构内容为空")
            
            # 6. 获取故事结构内容
            story_structure = self.db.query(StoryStructureDB).filter(
                StoryStructureDB.novel_id == novel_id,
                StoryStructureDB.is_complete == "true"
            ).order_by(StoryStructureDB.created_at.desc()).first()
            
            story_structure_content = ""
            if story_structure and story_structure.story_structures_inference:
                story_structure_content = story_structure.story_structures_inference
            else:
                logger.warning(f"小说 {novel_id} 没有找到完成的故事结构")
                story_structure_content = "暂无故事结构，请先生成故事结构。"
            
            # 7. 加载模板
            template = self._load_template('4.chapter_content.md')
            
            # 8. 准备模板替换参数
            replacements = {
                "正文标题": outline_fork.fork_title,  # {{正文标题}}=outline_forks->fork_title
                "故事结构": story_structure_content,  # {{故事结构}}=story_structures->story_structures_inference
                "当前章节序号": str(outline_fork.fork_index),  # {{当前章节序号}}=outline_forks->fork_index
                "当前大纲": str(outline_task.outline_index),  # {{当前大纲}}=outlines->outline_index
                "当前大纲主题": outline_task.outline_title,  # {{当前大纲主题}}=outlines->outline_title
                "大纲内容": chapter_structure_fork.chapter_structures_inference,  # {{大纲内容}}=chapter_structure_forks->chapter_structures_inference
                "章节细纲": outline_fork.outline_inference,  # {{章节细纲}}=outline_forks->outline_inference
            }
            
            # 9. 执行模板替换
            generated_prompt = self._apply_template_replacements(template, replacements)
            
            # 10. 构建生成参数
            generation_parameters = {
                "style": config("chapter_content.default_style", "default"),
                "word_count": config("chapter_content.default_word_count", 6000),
                "pov_character": config("chapter_content.default_pov_character", None),
                "outline_fork_id": outline_fork_id
            }
            
            # 11. 构建模板信息
            template_info = {
                "template_file": "4.chapter_content.md",
                "outline_fork_id": outline_fork_id,
                "outline_task_id": outline_task.id,
                "chapter_structure_fork_id": outline_task.chapter_structure_fork_id,
                "replacements": replacements
            }
            
            # 12. 验证必需字段并创建章节内容数据库记录
            if outline_fork.fork_index is None:
                raise ValueError(f"大纲分支 {outline_fork_id} 的 fork_index 不能为空")
            if not outline_fork.fork_title or outline_fork.fork_title.strip() == "":
                raise ValueError(f"大纲分支 {outline_fork_id} 的 fork_title 不能为空")
            
            chapter_content_id = get_next_id()
            
            chapter_content = ChapterContentDB(
                id=chapter_content_id,
                novel_id=novel_id,
                outline_fork_id=outline_fork_id,
                outline_forks_index=outline_fork.fork_index,
                outline_forks_title=outline_fork.fork_title.strip(),
                outline_type="standard",
                generated_prompt=generated_prompt,
                parameters=json.dumps(generation_parameters, ensure_ascii=False),
                template_info=json.dumps(template_info, ensure_ascii=False),
                status="pending",
                is_complete="false",
                callback_url=callback_url,
                created_at=datetime.now()
            )
            
            self.db.add(chapter_content)
            self.db.commit()
            self.db.refresh(chapter_content)
            
            # 13. 调用HTTP推理服务
            try:
                # 构建推理请求消息（system + user格式）
                messages = [
                    {
                        "role": "system",
                        "content": "你是一名专业的小说作家，擅长根据故事结构和章节细纲创作引人入胜的小说章节。请根据用户提供的信息，创作完整的小说章节内容。"
                    },
                    {
                        "role": "user", 
                        "content": generated_prompt
                    }
                ]
                
                # 创建推理任务并获取WebSocket订阅信息
                response = InferenceManager.create_inference_task(messages)
                
                if response.success:
                    # 构建webhook URL
                    webhook_base_url = config("webhook.base_url", "http://localhost:3300")
                    webhook_url = f"{webhook_base_url}/api/v1/callbacks/chapter-content/{chapter_content_id}"
                    
                    # 更新章节内容记录的WebSocket和webhook信息
                    chapter_content.websocket_url = response.websocket_url
                    chapter_content.websocket_task_id = response.task_id
                    chapter_content.status = "running"
                    self.db.commit()
                    
                    logger.info(f"章节内容推理任务已启动 - chapter_content_id: {chapter_content_id}, websocket_task_id: {response.task_id}")
                else:
                    # 推理任务创建失败
                    chapter_content.status = "failed"
                    chapter_content.error_message = f"推理任务创建失败: {response.message if hasattr(response, 'message') else 'Unknown error'}"
                    self.db.commit()
                    logger.error(f"章节内容推理任务创建失败 - chapter_content_id: {chapter_content_id}")
                    
            except Exception as inference_error:
                logger.error(f"调用推理服务失败 - chapter_content_id: {chapter_content_id}, 错误: {inference_error}")
                chapter_content.status = "failed"
                chapter_content.error_message = f"调用推理服务失败: {str(inference_error)}"
                self.db.commit()
            
            # 14. 构建返回结果
            result = {
                "chapter_content_id": chapter_content_id,
                "novel_id": novel_id,
                "novel_title": novel.title,
                "outline_fork_id": outline_fork_id,
                "outline_forks_title": outline_fork.fork_title,
                "outline_forks_index": outline_fork.fork_index,
                "outline_task_id": outline_task.id,
                "outline_index": outline_task.outline_index,
                "outline_title": outline_task.outline_title,
                "created_at": chapter_content.created_at.isoformat(),
                "status": chapter_content.status,
                "websocket_url": chapter_content.websocket_url,
                "websocket_task_id": chapter_content.websocket_task_id,
                "subscribe_action": "subscribeTask",
                "callback_url": callback_url
            }
            
            logger.info(f"章节内容生成任务已创建 - 小说: {novel.title} (ID: {novel_id}), 章节: {outline_fork.fork_index}, 章节内容ID: {chapter_content_id}")
            return result
            
        except Exception as e:
            logger.error(f"生成章节内容失败 - 小说ID: {novel_id}, 大纲分支ID: {outline_fork_id}, 错误: {e}")
            raise
    
    def get_chapter_content_by_outline_fork_id(self, novel_id: int, outline_fork_id: int) -> Optional[Dict[str, Any]]:
        """根据大纲分支ID获取章节内容"""
        try:
            chapter_content = self.db.query(ChapterContentDB).filter(
                ChapterContentDB.novel_id == novel_id,
                ChapterContentDB.outline_fork_id == outline_fork_id
            ).first()
            
            if not chapter_content:
                return None
            
            # 构建返回数据
            result = {
                "chapter_content_id": chapter_content.id,
                "novel_id": chapter_content.novel_id,
                "outline_fork_id": chapter_content.outline_fork_id,
                "outline_forks_index": chapter_content.outline_forks_index,
                "outline_forks_title": chapter_content.outline_forks_title,
                "outline_type": chapter_content.outline_type,
                "status": chapter_content.status,
                "is_complete": chapter_content.is_complete,
                "chapter_inference": chapter_content.chapter_inference,
                "word_count": chapter_content.word_count,
                "error_message": chapter_content.error_message,
                "websocket_url": chapter_content.websocket_url,
                "websocket_task_id": chapter_content.websocket_task_id,
                "callback_url": chapter_content.callback_url,
                "created_at": chapter_content.created_at.isoformat(),
                "updated_at": chapter_content.updated_at.isoformat(),
                "completed_at": chapter_content.completed_at.isoformat() if chapter_content.completed_at else None
            }
            
            # 注意：parameters、generated_prompt、template_info 等内部参数不返回给API
            
            return result
            
        except Exception as e:
            logger.error(f"获取章节内容失败 - 小说ID: {novel_id}, 大纲分支ID: {outline_fork_id}, 错误: {e}")
            raise
    
    def get_chapter_content_by_id(self, chapter_content_id: int) -> Optional[Dict[str, Any]]:
        """根据章节内容ID获取章节内容"""
        try:
            chapter_content = self.db.query(ChapterContentDB).filter(
                ChapterContentDB.id == chapter_content_id
            ).first()
            
            if not chapter_content:
                return None
            
            return self.get_chapter_content_by_outline_fork_id(
                chapter_content.novel_id, 
                chapter_content.outline_fork_id
            )
            
        except Exception as e:
            logger.error(f"获取章节内容失败 - 章节内容ID: {chapter_content_id}, 错误: {e}")
            raise
    
    def list_chapter_contents(self, novel_id: int, status: Optional[str] = None, 
                            outline_fork_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """获取小说的章节内容列表"""
        try:
            query = self.db.query(ChapterContentDB).filter(
                ChapterContentDB.novel_id == novel_id
            )
            
            if status:
                query = query.filter(ChapterContentDB.status == status)
            
            if outline_fork_id:
                query = query.filter(ChapterContentDB.outline_fork_id == outline_fork_id)
            
            # 按章节序号排序
            chapter_contents = query.order_by(ChapterContentDB.outline_forks_index.asc()).all()
            
            result = []
            for chapter_content in chapter_contents:
                item = {
                    "chapter_content_id": chapter_content.id,
                    "novel_id": chapter_content.novel_id,
                    "outline_fork_id": chapter_content.outline_fork_id,
                    "outline_forks_index": chapter_content.outline_forks_index,
                    "outline_forks_title": chapter_content.outline_forks_title,
                    "outline_type": chapter_content.outline_type,
                    "status": chapter_content.status,
                    "is_complete": chapter_content.is_complete,
                    "word_count": chapter_content.word_count,
                    "created_at": chapter_content.created_at.isoformat(),
                    "updated_at": chapter_content.updated_at.isoformat(),
                    "completed_at": chapter_content.completed_at.isoformat() if chapter_content.completed_at else None
                }
                result.append(item)
            
            return result
            
        except Exception as e:
            logger.error(f"获取章节内容列表失败 - 小说ID: {novel_id}, 错误: {e}")
            raise
    
    def update_chapter_content_result(self, chapter_content_id: int, 
                                    chapter_content: str, status: str = "completed", 
                                    error_message: Optional[str] = None) -> bool:
        """更新章节内容推理结果（用于WebSocket回调）"""
        try:
            chapter = self.db.query(ChapterContentDB).filter(
                ChapterContentDB.id == chapter_content_id
            ).first()
            
            if not chapter:
                logger.error(f"章节内容不存在 - ID: {chapter_content_id}")
                return False
            
            # 更新章节内容
            chapter.chapter_inference = chapter_content
            chapter.status = status
            chapter.error_message = error_message
            chapter.is_complete = "true" if status == "completed" else "false"
            chapter.updated_at = datetime.now()
            
            if status == "completed":
                chapter.completed_at = datetime.now()
                # 计算字数
                if chapter_content:
                    chapter.word_count = len(chapter_content.replace(" ", "").replace("\n", ""))
            
            self.db.commit()
            self.db.refresh(chapter)
            
            logger.info(f"章节内容更新成功 - ID: {chapter_content_id}, 状态: {status}")
            return True
            
        except Exception as e:
            logger.error(f"更新章节内容失败 - ID: {chapter_content_id}, 错误: {e}")
            self.db.rollback()
            return False
    
    def get_chapter_contents_by_outline_task_id(self, novel_id: int, outline_task_id: int) -> List[Dict[str, Any]]:
        """根据大纲任务ID获取章节内容列表"""
        try:
            # 先获取该大纲任务下的所有大纲分支
            outline_forks = self.db.query(OutlineForkDB).filter(
                OutlineForkDB.outline_task_id == outline_task_id,
                OutlineForkDB.novel_id == novel_id
            ).all()
            
            if not outline_forks:
                return []
            
            outline_fork_ids = [fork.id for fork in outline_forks]
            
            # 获取这些大纲分支对应的章节内容
            chapter_contents = self.db.query(ChapterContentDB).filter(
                ChapterContentDB.novel_id == novel_id,
                ChapterContentDB.outline_fork_id.in_(outline_fork_ids)
            ).order_by(ChapterContentDB.outline_forks_index.asc()).all()
            
            result = []
            for chapter_content in chapter_contents:
                item = {
                    "chapter_content_id": chapter_content.id,
                    "novel_id": chapter_content.novel_id,
                    "outline_fork_id": chapter_content.outline_fork_id,
                    "outline_forks_index": chapter_content.outline_forks_index,
                    "outline_forks_title": chapter_content.outline_forks_title,
                    "outline_type": chapter_content.outline_type,
                    "status": chapter_content.status,
                    "is_complete": chapter_content.is_complete,
                    "word_count": chapter_content.word_count,
                    "created_at": chapter_content.created_at.isoformat(),
                    "updated_at": chapter_content.updated_at.isoformat(),
                    "completed_at": chapter_content.completed_at.isoformat() if chapter_content.completed_at else None
                }
                result.append(item)
            
            return result
            
        except Exception as e:
            logger.error(f"根据大纲任务ID获取章节内容列表失败 - 小说ID: {novel_id}, 大纲任务ID: {outline_task_id}, 错误: {e}")
            raise 