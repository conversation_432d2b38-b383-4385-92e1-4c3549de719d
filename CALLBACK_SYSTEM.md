# AI Novel Generator - 外部回调系统

## 概述

AI Novel Generator 支持外部回调功能，当任务完成时自动向指定的URL发送HTTP POST请求，通知外部系统任务执行结果。

## 🚀 核心特性

- **异步回调**: 使用独立线程处理，不阻塞API响应
- **自动重试**: 失败时自动重试，递增间隔时间
- **容错处理**: 回调失败不影响主要业务流程
- **完整日志**: 详细记录回调执行过程
- **多任务类型支持**: 支持不同类型的任务回调

## 支持的任务类型

### 1. 故事结构生成 (story_structure)

**API端点**:
```
POST /api/v1/novels/{novel_id}/generate/story-structure?callback_url={your_callback_url}
```

**回调触发时机**: 故事结构生成完成或失败时

### 2. 章节结构生成回调 (chapter_structure) 🆕

**API端点**:
```
POST /api/v1/novels/{novel_id}/generate/chapter-structure?callback_url={your_callback_url}
```

**回调触发时机**: 章节结构生成过程中每个大纲完成时（中间状态）和最终完成时

**回调特性**:
- **实时回调**: 每完成一个大纲立即回调一次
- **多次回调**: 如果有28个大纲，将收到28次中间状态回调 + 1次最终完成回调
- **详细信息**: 每次回调包含完整的分支信息

## 回调机制详解

### 1. 回调请求格式

```http
POST {callback_url}
Content-Type: application/json
User-Agent: AI-Novel-Callback/1.0

{
    "task_id": "123456789",
    "task_type": "story_structure", 
    "novel_id": 1001,
    "status": "success",
    "result": "生成的故事结构内容...",
    "timestamp": 1699999999.999
}
```

### 2. 回调参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| `task_id` | string | 任务唯一标识符 |
| `original_task_id` | string | 原始任务ID（可选，v2.0新增） |
| `task_type` | string | 任务类型（如: story_structure, chapter_structure） |
| `status` | string | 任务状态：`success`、`failed`、`completed_section` |
| `section_type` | string | 章节类型（仅completed_section状态时包含，v2.0新增） |
| `result` | string | 生成结果内容（仅成功时包含） |
| `error_message` | string | 错误描述（仅失败时包含） |
| `timestamp` | float | Unix时间戳 |
| `fork_id` | string | 分支ID（可选，用于章节结构等有分支的任务） |
| `promptTokens` | integer | 提示词Token数量（v2.0新增） |
| `completionTokens` | integer | 生成内容Token数量（v2.0新增） |
| `totalTokens` | integer | 总Token数量（v2.0新增） |
| `fork_data` | object | 分支详细数据（仅章节结构completed_section状态时包含，v2.1新增） |

### 🆕 回调状态类型（v2.0更新）

#### 1. `success` - 最终完成状态
任务完全完成，包含最终生成结果：
```json
{
    "task_id": "9301916112691200",
    "status": "success",
    "result": "完整的生成内容...",
    "task_type": "chapter_structure",
    "promptTokens": 104,
    "completionTokens": 4888,
    "totalTokens": 4992,
    "timestamp": 1640995200000
}
```

#### 2. `completed_section` - 中间进度状态 🆕
任务执行过程中的中间状态，每个大纲完成时触发：
```json
{
    "task_id": "9301916112691200",
    "original_task_id": "9301916112691200", 
    "status": "completed_section",
    "section_type": "completed_content",
    "result": "已完成的章节内容...",
    "task_type": "chapter_structure",
    "promptTokens": 0,
    "completionTokens": 2500,
    "totalTokens": 2500,
    "timestamp": 1640995200000,
    "fork_data": {
        "chapter_structure_task_id": "9301916112691200",
        "fork_id": "9301916112691201",
        "fork_index": 1,
        "fork_title": "大纲1：初入仙途",
        "structure_type": "section",
        "fork_status": "completed",
        "created_at": "2024-06-18T17:42:10.232",
        "completed_at": "2024-06-18T17:42:10.232"
    }
}
```

#### 3. `failed` - 失败状态
任务执行失败：
```json
{
    "task_id": "9301916112691200",
    "status": "failed",
    "error_message": "推理服务连接超时",
    "task_type": "chapter_structure",
    "timestamp": 1640995200000
}
```

### 回调处理逻辑更新

#### 累积内容处理 🔄 
推理服务的回调是累积的，每次都包含之前的所有内容：
- **第1次回调**: 包含大纲1 
- **第2次回调**: 包含大纲1 + 大纲2
- **第3次回调**: 包含大纲1 + 大纲2 + 大纲3

**智能提取逻辑**:
```python
# 系统会自动提取最新的章节部分
def _extract_latest_section_from_cumulative_content(content, existing_fork_count):
    sections = content.split('§§')  # 按§§符号拆分
    valid_sections = [s.strip() for s in sections if s.strip()]
    
    # 获取下一个新章节
    if len(valid_sections) > existing_fork_count:
        return valid_sections[existing_fork_count]  # 提取最新部分
    else:
        return valid_sections[-1]  # 返回最后一个章节
```

**标题清理**:
- 自动移除特殊标识符：`§§`、`📖`
- 处理多级markdown标题：`###`、`##`、`#`
- 智能提取包含"大纲"的标题行
- 清理后的标题更加简洁易读

#### 中间状态处理 🆕
系统现在支持处理中间状态回调 `completed_section`：
- **智能提取**: 从累积内容中自动提取最新的章节部分
- **避免重复**: 只保存新增的章节内容，不重复入库
- **实时保存**：推理过程中生成的内容立即保存，避免数据丢失
- **分支索引**：自动分配递增的分支索引和智能标题提取
- **日志追踪**：记录详细的中间状态处理日志

#### 最终状态处理
- **`success` 状态**：只更新 `chapter_structures` 任务状态为完成，不重复处理分支
- **`failed` 状态**：更新任务状态为失败并触发外部回调
- **外部回调**：只有最终状态才触发外部 callback_url 通知

#### 处理流程更新 🔄

**新的处理流程**：
1. **中间状态 `completed_section`**：
   - 接收累积的章节结构内容
   - 智能提取最新的章节部分
   - 立即创建 `chapter_structure_forks` 记录
   - 自动分配分支索引和提取标题
   - 记录日志但不触发外部回调

2. **最终状态 `success`**：
   - 首先检查并处理最后一个章节（如果有未入库的）
   - 智能避免重复：如果最后章节已在中间状态处理，则跳过
   - 更新 `chapter_structures` 任务状态为 `completed`
   - 设置 `is_complete = "true"`
   - 触发外部回调（如果有 callback_url）

3. **失败状态 `failed`**：
   - 更新任务状态为失败
   - 触发外部回调通知失败情况

**智能防重复机制**：
```python
# 检查是否有新章节需要处理
if len(valid_sections) <= existing_forks_count:
    logger.info("没有新章节需要创建")
    return False  # 跳过重复处理

# 只有真正有新内容时才创建分支
if latest_content and latest_content.strip():
    create_new_fork()
```

### 3. 成功回调示例

```json
{
    "task_id": "1234567890123456789",
    "task_type": "story_structure",
    "novel_id": 1001,
    "status": "success",
    "result": "# 故事结构\n\n## 主题\n现代都市修仙...",
    "timestamp": 1699999999.999
}
```

### 4. 失败回调示例

```json
{
    "task_id": "1234567890123456789",
    "task_type": "story_structure",
    "novel_id": 1001,
    "status": "failed",
    "error_message": "推理服务连接超时",
    "timestamp": 1699999999.999
}
```

## 重试机制

### 重试策略

- **重试间隔**: 0秒、2秒、4秒、16秒、256秒
- **最大重试次数**: 5次
- **成功条件**: HTTP状态码 < 400
- **超时时间**: 30秒

### 重试流程

1. **第1次**: 立即发送（0秒延迟）
2. **第2次**: 2秒后重试
3. **第3次**: 4秒后重试
4. **第4次**: 16秒后重试
5. **第5次**: 256秒后重试
6. **放弃**: 所有重试失败后停止

### 重试日志示例

```
INFO: 发送回调请求 - 尝试 1/5 - URL: https://example.com/webhook
WARNING: 回调失败 - URL: https://example.com/webhook, 状态码: 500
INFO: 回调重试 2/5 - 等待 2 秒 - URL: https://example.com/webhook
INFO: 回调成功 - URL: https://example.com/webhook, 状态码: 200
```

## 使用指南

### 1. 基本使用

```bash
# 发起带回调的故事结构生成请求
curl -X POST "http://localhost:3300/api/v1/novels/1001/generate/story-structure?callback_url=https://your-domain.com/webhook/story-complete" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 2. 回调端点实现示例

#### Python Flask 示例

```python
from flask import Flask, request, jsonify
import logging

app = Flask(__name__)
logger = logging.getLogger(__name__)

@app.route('/webhook/story-complete', methods=['POST'])
def handle_story_complete():
    try:
        data = request.get_json()
        
        task_id = data['task_id']
        task_type = data['task_type']
        novel_id = data['novel_id']
        status = data['status']
        
        if status == 'success':
            result = data['result']
            logger.info(f"故事结构生成成功 - task_id: {task_id}, novel_id: {novel_id}")
            # 处理成功结果
            process_story_structure(novel_id, result)
        else:
            error_message = data['error_message']
            logger.error(f"故事结构生成失败 - task_id: {task_id}, error: {error_message}")
            # 处理失败情况
            handle_generation_failure(novel_id, error_message)
        
        return jsonify({"status": "ok"}), 200
        
    except Exception as e:
        logger.error(f"处理回调失败: {e}")
        return jsonify({"error": "Internal server error"}), 500

def process_story_structure(novel_id, result):
    # 处理生成的故事结构
    pass

def handle_generation_failure(novel_id, error_message):
    # 处理生成失败的情况
    pass
```

#### Node.js Express 示例

```javascript
const express = require('express');
const app = express();

app.use(express.json());

app.post('/webhook/story-complete', (req, res) => {
    try {
        const { task_id, task_type, novel_id, status, result, error_message, timestamp } = req.body;
        
        console.log(`收到回调 - task_id: ${task_id}, status: ${status}`);
        
        if (status === 'success') {
            // 处理成功结果
            console.log(`故事结构生成成功 - novel_id: ${novel_id}`);
            processStoryStructure(novel_id, result);
        } else {
            // 处理失败情况
            console.error(`故事结构生成失败 - novel_id: ${novel_id}, error: ${error_message}`);
            handleGenerationFailure(novel_id, error_message);
        }
        
        res.status(200).json({ status: 'ok' });
        
    } catch (error) {
        console.error('处理回调失败:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

function processStoryStructure(novelId, result) {
    // 处理生成的故事结构
}

function handleGenerationFailure(novelId, errorMessage) {
    // 处理生成失败的情况
}

app.listen(3000, () => {
    console.log('回调服务器运行在端口 3000');
});
```

### 3. 安全建议

#### 验证回调来源

```python
import hmac
import hashlib

def verify_callback_signature(payload, signature, secret_key):
    """验证回调签名（如果需要）"""
    expected_signature = hmac.new(
        secret_key.encode('utf-8'),
        payload.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    
    return hmac.compare_digest(signature, expected_signature)
```

#### IP白名单

```python
ALLOWED_IPS = ['**************', '127.0.0.1']  # AI Novel Generator服务器IP

@app.before_request
def limit_remote_addr():
    if request.remote_addr not in ALLOWED_IPS:
        abort(403)  # Forbidden
```

## 故障排除

### 常见问题

1. **回调URL无法访问**
   - 检查URL是否正确
   - 确保服务器可以访问外网
   - 验证防火墙设置

2. **回调请求超时**
   - 检查目标服务器响应时间
   - 确保回调端点能在30秒内响应

3. **回调重复接收**
   - 实现幂等性处理
   - 使用task_id去重

### 调试技巧

1. **查看日志**
```bash
# 查看回调相关日志
grep "回调" logs/app.log
grep "callback" logs/app.log
```

2. **测试回调端点**
```bash
# 手动测试回调端点
curl -X POST "https://your-domain.com/webhook/story-complete" \
  -H "Content-Type: application/json" \
  -d '{
    "task_id": "test_123",
    "task_type": "story_structure",
    "novel_id": 1001,
    "status": "success",
    "result": "测试内容",
    "timestamp": 1699999999.999
  }'
```

## 最佳实践

### 1. 回调端点设计

- **快速响应**: 回调端点应在30秒内响应
- **幂等性**: 支持重复调用而不产生副作用
- **错误处理**: 妥善处理异常情况
- **日志记录**: 记录所有回调请求

### 2. 安全考虑

- **HTTPS**: 使用HTTPS保护回调数据
- **验证**: 验证回调来源和数据完整性
- **限流**: 实现适当的请求限流
- **监控**: 监控回调成功率和响应时间

### 3. 性能优化

- **异步处理**: 回调端点应异步处理业务逻辑
- **队列机制**: 使用消息队列处理大量回调
- **缓存**: 适当使用缓存减少数据库压力

## 技术实现

### 回调服务架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   API请求       │    │   任务处理       │    │   回调服务      │
│  (带callback)   │───▶│   (异步执行)     │───▶│  (独立线程)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │                         │
                              ▼                         ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │   数据库更新     │    │   HTTP POST     │
                       │  (任务状态)      │    │  (外部系统)     │
                       └──────────────────┘    └─────────────────┘
```

### 核心组件

1. **CallbackService**: 回调服务核心类
2. **独立线程池**: 处理并发回调请求
3. **重试机制**: 指数退避重试策略
4. **日志系统**: 完整的执行日志

---

## 更新日志

- **v1.0.0** (2024-01-01): 初始版本，支持故事结构生成回调
- **v1.1.0** (计划中): 支持更多任务类型回调 

## 🔧 配置参数（可根据需要调整）：

```python
MAX_WORKERS = 10                    # 线程池大小
POOL_CONNECTIONS = 20              # 连接池大小  
_max_concurrent_callbacks = 50     # 最大并发数
```

## ⚡ 多进程部署优化

### 自动资源分配

回调服务现在支持多进程环境的智能资源分配：

#### 单进程模式（默认）
```bash
# 启动单个进程
uvicorn app.main:app --host 0.0.0.0 --port 3300
```
- **线程池**: 4-10个线程（基于CPU核心数）
- **连接池**: 20个连接
- **并发数**: 50个回调

#### 多进程模式
```bash
# 启动4个进程
uvicorn app.main:app --host 0.0.0.0 --port 3300 --workers 4

# 或使用环境变量
export WEB_CONCURRENCY=4
uvicorn app.main:app --host 0.0.0.0 --port 3300
```
- **每进程线程池**: 2-3个线程（总计8-12个）
- **每进程连接池**: 5个连接（总计20个）
- **每进程并发数**: 10个回调（总计40个）

### 环境变量配置

可以通过环境变量精确控制资源分配：

```bash
# 自定义回调服务配置
export CALLBACK_MAX_WORKERS=8           # 每进程最大线程数
export CALLBACK_POOL_CONNECTIONS=15     # 每进程连接池大小
export CALLBACK_POOL_MAXSIZE=15         # 每host最大连接数
export CALLBACK_MAX_CONCURRENT=25       # 每进程最大并发回调数

# 启动应用
uvicorn app.main:app --workers 4
```

### 资源分配算法

```python
# 单进程模式
max_workers = min(10, max(4, cpu_count // 2))
pool_connections = 20
max_concurrent_callbacks = 50

# 多进程模式
max_workers = max(2, cpu_count // worker_processes)
pool_connections = max(5, 15 // worker_processes)
max_concurrent_callbacks = max(10, 30 // worker_processes)
```

### 监控多进程状态

使用健康检查API监控多进程环境：

```bash
curl http://localhost:3300/api/v1/callbacks/health
```

**响应示例（多进程环境）**：
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "environment": {
      "cpu_count": 8,
      "worker_processes": 4,
      "is_multiprocess": true,
      "deployment_mode": "多进程模式"
    },
    "resource_allocation": {
      "threads_per_process": 2,
      "total_threads_estimated": 8,
      "connections_per_process": 5,
      "total_connections_estimated": 20,
      "concurrent_callbacks_per_process": 10,
      "total_concurrent_estimated": 40
    },
    "performance_info": {
      "resource_efficiency": "优化"
    }
  }
}
```

### 生产环境建议

#### 高并发场景
```bash
# 推荐配置：4-8个进程
export WEB_CONCURRENCY=4
export CALLBACK_MAX_WORKERS=4
export CALLBACK_MAX_CONCURRENT=20
uvicorn app.main:app --workers 4
```

#### 低延迟场景
```bash
# 推荐配置：2个进程，更多线程
export WEB_CONCURRENCY=2
export CALLBACK_MAX_WORKERS=8
export CALLBACK_MAX_CONCURRENT=30
uvicorn app.main:app --workers 2
```

#### 资源受限环境
```bash
# 推荐配置：单进程，减少线程
export CALLBACK_MAX_WORKERS=4
export CALLBACK_MAX_CONCURRENT=20
uvicorn app.main:app
```

## 🚀 核心优化：

### 1. **线程池管理** - 使用 `ThreadPoolExecutor`（10个工作线程） 

## 章节结构回调系统升级 (2024年新增)

### 核心创新功能

#### 1. 智能内容拆分
```python
# 从配置中获取全局分隔符（可配置，默认为§§）
delimiter = config("content_parsing.section_delimiter", "§§")
sections = content.split(delimiter)

# 每个大纲生成独立的数据库记录：
# - ChapterStructureForkDB.fork_index: 大纲序号
# - ChapterStructureForkDB.fork_title: 自动提取的标题  
# - ChapterStructureForkDB.chapter_structures_inference: 完整大纲内容
```

#### 2. 支持的内容格式
```
{{section_delimiter}}
### 📖 大纲1：初入仙途
* **元数据**: 范围 第1章 - 第36章 | 共36章
* **概述**: 主角出身平凡，父母早亡后被清风阁收留...
---
{{section_delimiter}} 
### 📖 大纲2：历练磨难
* **元数据**: 范围 第37章 - 第72章 | 共36章
* **概述**: 主角摆脱血魔殿杀手后...
---
```

**全局分隔符配置** 🆕:
- **配置文件**: `app/config/application.yaml`
- **配置路径**: `content_parsing.section_delimiter`
- **默认值**: `§§`
- **应用范围**: 系统全局所有内容分割场景
- **自定义示例**:
  ```yaml
  content_parsing:
    section_delimiter: "---OUTLINE---"  # 自定义全局分隔符
  ```

#### 3. 数据库表结构更新
```sql
-- 新增字段到 chapter_structures 表
ALTER TABLE `chapter_structures` 
ADD COLUMN `callback_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '外部回调URL' 
AFTER `error_message`;
```

### API使用示例

#### 基本调用
```bash
# 生成章节结构（带回调）
POST /api/v1/novels/123/generate/chapter-structure?callback_url=https://your-domain.com/webhook/chapter-complete

# 查询生成的分支列表
GET /api/v1/novels/123/chapter-structure-forks

# 查询特定分支详情
GET /api/v1/novels/123/chapter-structure-forks/456
```

#### 回调数据格式
```json
{
    "task_id": "123456789",
    "task_type": "chapter_structure",
    "novel_id": 1001,
    "status": "success",
    "result": "§§\n### 📖 大纲1：初入仙途\n...",
    "timestamp": 1699999999.999
}
```

### 处理流程

1. **API调用**: 带callback_url参数调用章节结构生成API
2. **任务创建**: 创建ChapterStructureDB任务记录，保存callback_url
3. **HTTP推理**: 调用推理服务，设置内部webhook URL
4. **内容接收**: 推理完成后接收完整的章节结构内容
5. **智能拆分**: 按配置化的分隔符拆分成多个大纲
6. **分支创建**: 为每个大纲创建独立的ChapterStructureForkDB记录
7. **外部回调**: 向callback_url发送完成通知

### 代码架构

#### 服务层新增方法
- `_parse_chapter_structure_content()`: 智能内容拆分
- `get_chapter_structure_from_db_by_task_id()`: 获取任务信息
- `update_chapter_structure_result()`: 更新结果并创建分支

#### 回调服务新增方法  
- `create_chapter_structure_callback_payload()`: 创建回调数据
- `send_chapter_structure_callback()`: 发送外部回调