# AI Novel Generator

## 项目概览

AI Novel Generator 是一个基于 FastAPI 构建的 AI 辅助小说创作平台，旨在帮助作者生成、管理和提炼小说内容。

## 🚀 核心特性

- **统一配置系统**: 基于YAML的配置管理，支持点分隔路径访问
- **智能启动系统**: 优化的启动脚本，支持环境变量配置
- **WebSocket推理集成**: 与外部LLM推理服务的实时集成，支持完整推理流程
- **Web客户端**: 集成LLM推理服务客户端，支持WebSocket实时对话
- **统一错误处理**: 标准化的异常处理和API响应格式
- **雪花ID系统**: 分布式唯一ID生成器
- **混入类架构**: 消除数据库表重复字段
- **基础服务类**: 提供通用CRUD操作

## 项目架构

```
ai-novel/
├── app/                  # 主应用目录
│   ├── config/           # 配置系统
│   │   ├── application.yaml      # YAML配置文件
│   │   ├── configuration.py      # 配置管理器
│   │   └── database.py           # 数据库配置
│   ├── models/           # 数据模型
│   ├── routers/          # API路由
│   ├── services/         # 业务逻辑
│   ├── utils/            # 工具函数
│   │   └── inference_websocket.py  # WebSocket推理工具（已优化）
│   └── main.py           # 应用入口
├── client/               # Web客户端
├── demo/                 # WebSocket使用示例
│   ├── quick_demo.py     # 简单推理示例
│   ├── python_client_demo.py    # 完整推理客户端
│   └── README_python_client.md  # 使用说明
├── scripts/              # 管理脚本
└── requirements.txt      # 项目依赖
```

## 核心功能

1. **小说内容生成** - AI生成小说情节、对话和描述
2. **小说管理** - 完整的CRUD操作
3. **内容提取** - 提取关键信息、角色和情节
4. **WebSocket推理** - 与外部LLM推理服务的完整集成
5. **Web客户端** - WebSocket实时对话界面
6. **统一配置管理** - 支持热重载的配置系统

## WebSocket推理系统

### 🔄 **推理流程**
1. **连接WebSocket** → 收到连接确认 (`connected`)
2. **发送推理请求** → 使用 `{"action": "infer", "prompt": "..."}`
3. **任务创建** → 收到任务ID (`taskCreated`)
4. **推理开始** → 收到第一个token (`token`)
5. **自动断开** → 推理任务已启动，用户可监听结果

### 💡 **技术特点**
- **后台线程处理**: 非阻塞的WebSocket连接
- **智能超时机制**: 每个步骤都有独立的超时控制
- **完整状态管理**: 连接、任务创建、推理开始的状态跟踪
- **错误恢复**: 完善的错误处理和日志记录

### 📋 **使用示例**
```python
from app.utils.inference_websocket import InferenceWebSocketManager

# 生成连接信息
ws_info = InferenceWebSocketManager.generate_connection_info("task_123")

# 启动推理任务
task_id = InferenceWebSocketManager.start_inference_task(
    ws_url=ws_info.url,
    prompt="请写一个故事大纲",
    task_id="task_123"
)

print(f"推理任务ID: {task_id}")  # 输出：推理任务ID: 12036589341704225
```

## 技术栈

- **后端**: FastAPI + Uvicorn + SQLAlchemy + MySQL
- **前端**: 原生HTML/JS WebSocket客户端
- **推理集成**: websocket-client（推理任务发起）+ 外部LLM服务
- **数据验证**: Pydantic（支持邮箱验证）
- **认证**: JWT + passlib
- **配置**: 自定义配置系统 + PyYAML

## 快速开始

### 1. 环境准备
```bash
# 创建虚拟环境（推荐）
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置应用
编辑 `app/config/application.yaml` 配置数据库、JWT密钥和WebSocket推理服务

### 3. 启动应用
```bash
# 推荐方式：使用智能启动脚本（默认3300端口）
bash scripts/run.sh

# 自定义配置启动
export PORT=8080
export WORKERS=4
bash scripts/run.sh

# 生产环境启动
DEPLOYMENT_MODE=production bash scripts/run.sh

# 直接启动（仅用于开发测试）
python -m app.main
```

### 4. 访问应用
- **API文档**: `http://localhost:3300/docs`
- **Web客户端**: `http://localhost:3300/web`
- **健康检查**: `http://localhost:3300/health`
- **回调服务监控**: `http://localhost:3300/api/v1/callbacks/health`

## 智能启动系统

### 🚀 **启动脚本特性**
- **智能资源分配**: 根据系统CPU和内存自动优化进程数和线程配置
- **虚拟环境自动检测**: 自动检测并激活 `venv` 虚拟环境
- **多平台兼容**: 专门优化Mac和Ubuntu环境
- **进程管理**: 支持单进程和多进程部署模式
- **端口管理**: 自动检查端口占用并提供释放选项
- **健康监控**: 内置应用状态监控和自动重启

### 📋 **启动脚本命令**
```bash
bash scripts/run.sh [command] [options]

# 基础命令:
start     # 启动应用（默认）
stop      # 停止应用
restart   # 重启应用
status    # 查看运行状态和性能信息
monitor   # 监控模式（自动重启）
clean     # 清理Python缓存文件
help      # 显示详细帮助信息
```

### ⚙️ **环境变量配置**
```bash
# 基础配置
export PORT=3300                    # 端口号（默认3300）
export HOST=0.0.0.0                 # 主机地址

# 智能资源配置（推荐使用auto）
export WORKERS=auto                 # 工作进程数（auto=智能计算）
export DEPLOYMENT_MODE=development  # 部署模式（development/production）

# 回调服务配置（通常无需手动设置）
export CALLBACK_MAX_WORKERS=auto       # 回调线程数
export CALLBACK_POOL_CONNECTIONS=auto  # 连接池大小
export CALLBACK_MAX_CONCURRENT=auto    # 最大并发数
```

### 🎯 **智能资源分配算法**
启动脚本会根据系统资源自动计算最优配置：

**工作进程数计算:**
- 基于CPU核心数、可用内存和系统负载
- 开发环境：`进程数 = min(CPU核心数, 可用内存/250MB, 16)`
- 生产环境：`进程数 = min(CPU核心数-1, 可用内存/400MB, 16)`
- 高负载时自动减半进程数

**回调服务配置:**
- 单进程模式：`线程数 = CPU核心数/2`
- 多进程模式：`线程数 = CPU核心数/工作进程数`
- 连接池和并发数按进程数等比例分配

### 📊 **资源监控示例**
```bash
# 查看当前运行状态
bash scripts/run.sh status

# 输出示例：
# 应用正在运行
# 端口: 3300
# 进程数: 4
#   PID: 12345, 运行时间: 01:23:45, CPU: 2.3%, 内存: 256 MB
#   PID: 12346, 运行时间: 01:23:45, CPU: 1.8%, 内存: 234 MB
# 
# 回调服务状态:
# {
#   "status": "healthy",
#   "environment": {
#     "deployment_mode": "多进程模式",
#     "worker_processes": 4
#   },
#   "resource_allocation": {
#     "total_threads_estimated": 16,
#     "total_connections_estimated": 80
#   }
# }
```

### 🔧 **配置示例**
```bash
# 完全自动配置（推荐）
bash scripts/run.sh start

# 自定义端口，其他自动
PORT=8080 bash scripts/run.sh start

# 生产环境自动配置
DEPLOYMENT_MODE=production bash scripts/run.sh start

# 手动指定进程数，其他自动
WORKERS=6 bash scripts/run.sh start

# 资源受限环境
WORKERS=2 CALLBACK_MAX_WORKERS=2 bash scripts/run.sh start
```

### 🛠️ **系统要求**
- **最小内存**: 1GB（单进程模式）
- **推荐内存**: 2GB+（多进程模式）
- **推荐CPU**: 2核心+
- **操作系统**: macOS 或 Ubuntu（已优化兼容）
- **Python**: 3.8+
- **依赖命令**: `lsof`（用于端口管理，通常系统自带）

## API端点

### 小说管理
- `POST /api/v1/novels` - 创建小说
- `GET /api/v1/novels` - 获取小说列表
- `GET /api/v1/novels/{id}` - 获取小说详情
- `PUT /api/v1/novels/{id}` - 更新小说
- `DELETE /api/v1/novels/{id}` - 删除小说

### 内容生成（HTTP推理集成）
- `POST /api/v1/novels/{id}/generate/story-structure` - 生成故事结构（HTTP创建任务+配置文件WebSocket URL）
- `POST /api/v1/novels/{id}/generate/chapter-structure` - 生成章节结构
- `POST /api/v1/novels/{id}/generate/outline` - 生成大纲
- `POST /api/v1/novels/{id}/generate/chapter-content` - 生成章节内容 🆕

### 内容查询
- `GET /api/v1/novels/{id}/generate/story-structure` - 获取故事结构
- `GET /api/v1/novels/{id}/chapter-structure-forks` - 获取章节结构分支列表
- `GET /api/v1/novels/{id}/chapter-structure-forks/{fork_id}` - 获取章节结构分支详情
- `GET /api/v1/novels/{id}/generate/outline` - 获取大纲任务信息
- `GET /api/v1/novels/{id}/outline-tasks/{task_id}/forks` - 获取大纲分支列表
- `GET /api/v1/novels/{id}/outline-forks/{fork_id}` - 获取大纲分支详情
- `GET /api/v1/novels/{id}/chapter-content` - 获取单个章节内容 🆕
- `GET /api/v1/novels/{id}/chapter-contents` - 获取整个小说的章节内容列表 🆕
- `GET /api/v1/novels/{id}/outline-tasks/{task_id}/chapter-contents` - 获取大纲的章节内容列表 🆕

### 外部回调功能
- **支持自定义回调URL**: 任务完成后自动通知外部系统
- **自动重试机制**: 失败时自动重试，最多5次
- **异步处理**: 使用独立线程，不阻塞API响应

#### 回调URL使用示例
```bash
# 带回调URL的故事结构生成
POST /api/v1/novels/1001/generate/story-structure?callback_url=https://your-domain.com/webhook/story-complete
```

#### 回调请求格式
```json
POST {callback_url}
Content-Type: application/json
User-Agent: AI-Novel-Callback/1.0

{
    "task_id": "123456789",
    "task_type": "story_structure", 
    "novel_id": 1001,
    "status": "success",  // 或 "failed"
    "result": "生成的故事结构内容...",  // 成功时包含
    "error_message": "错误描述",  // 失败时包含
    "timestamp": 1699999999.999
}
```

#### 回调重试策略
- **重试间隔**: 0秒、2秒、4秒、16秒、256秒
- **最大重试次数**: 5次
- **成功条件**: HTTP状态码 < 400
- **超时时间**: 30秒

### Web客户端
- `GET /web` - 重定向到客户端界面
- `GET /api/v1/client-info` - 获取客户端信息
- `GET /api/v1/client-status` - 检查客户端状态

### 认证系统
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录

## HTTP推理配置

### 配置文件设置 (`app/config/application.yaml`)
```yaml
# HTTP推理配置
http_inference:
  host: "**************"  # 推理服务主机地址
  port: 3000              # 推理服务端口
  api_path: "/api"        # API路径前缀
  timeout: 30             # HTTP请求超时时间（秒）
  max_retries: 3          # 最大重试次数
  retry_delay: 2          # 重试间隔（秒）
  
# WebSocket订阅配置（用户自行处理WebSocket订阅）
websocket_subscription:
  host: "**************"  # WebSocket服务主机地址
  port: 3000              # WebSocket服务端口
  path: "/ws"             # WebSocket路径
  timeout: 10             # 连接超时时间（秒）

# Webhook回调配置
webhook:
  base_url: "http://localhost:3300"  # 本服务的基础URL，用于构建回调地址

# 章节内容生成配置 🆕
chapter_content:
  default_style: "default"         # 默认写作风格
  default_word_count: 6000        # 默认目标字数
  default_pov_character: null     # 默认视角角色（null表示自动选择）
```

### 推理完成自动通知

系统支持推理完成后的自动回调通知：

1. **创建任务时自动设置webhook**：HTTP推理任务创建时会自动传递webhook URL
2. **推理服务完成后回调**：推理服务完成后会POST到 `/api/v1/callbacks/story-structure/{task_id}`
3. **自动更新数据库状态**：回调处理器会自动更新任务状态和结果到数据库

**回调端点**：
- `POST /api/v1/callbacks/story-structure/{task_id}` - 故事结构生成完成回调
- `POST /api/v1/callbacks/chapter-structure/{task_id}` - 章节结构生成完成回调
- `POST /api/v1/callbacks/outline/{task_id}` - 大纲生成完成回调
- `POST /api/v1/callbacks/chapter-content/{chapter_content_id}` - 章节内容生成完成回调 🆕

## WebSocket推理测试

### 运行快速示例
```bash
cd demo
python quick_demo.py
```

### 运行完整客户端
```bash
cd demo
python python_client_demo.py --host ************** --port 3000 --prompt "写一首诗"
```

### 测试API集成
```bash
# 测试WebSocket推理工具
python -c "
from app.utils.inference_websocket import InferenceWebSocketManager
ws_info = InferenceWebSocketManager.generate_connection_info('test')
task_id = InferenceWebSocketManager.start_inference_task(
    ws_url=ws_info.url, prompt='你好', task_id='test'
)
print(f'任务ID: {task_id}')
"
```

## HTTP推理测试

### 测试HTTP推理API
```bash
# 测试HTTP推理创建任务
python -c "
from app.utils.inference_websocket import InferenceManager
messages = [{'role': 'user', 'content': '你好，写一首诗'}]
response = InferenceManager.create_inference_task(messages)
print(f'创建成功: {response.success}')
print(f'任务ID: {response.task_id}')
print(f'WebSocket URL: {response.websocket_url}')
print(f'订阅动作: {response.subscribe_action}')
"
```

### 健康检查
```bash
python -c "
from app.utils.inference_websocket import InferenceManager
status = InferenceManager.health_check()
print(f'服务状态: {\"正常\" if status else \"异常\"}')
"
```

## 使用示例

### 创建小说
```bash
curl -X POST "http://localhost:3300/api/v1/novels" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "我的小说",
    "config": {
      "genre": "仙侠",
      "cultivation_system": "传统修仙"
    }
  }'
```

### 生成故事结构（HTTP推理方式）
```bash
curl -X POST "http://localhost:3300/api/v1/novels/1/generate/story-structure" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token"
```

**返回示例:**
```json
{
  "success": true,
  "message": "故事结构生成成功",
  "data": {
    "novel_id": 1,
    "generation_type": "story_structure",
    "content": {
      "novel_id": 1,
      "novel_title": "我的仙侠小说",
      "story_structure_id": 9202644106743808,
      "created_at": "2025-06-17T13:52:23",
      "task_id": "17819329309638707",
      "websocket_url": "ws://**************:3000/ws",
      "subscribe_action": "subscribeTask"
    },
    "is_complete": false
  }
}
```

**说明:**
- `task_id`: HTTP API创建的推理任务ID
- `websocket_url`: 从配置文件`websocket_subscription`读取的WebSocket URL
- `subscribe_action`: 订阅任务时使用的动作名称

**订阅推理结果:**
```javascript
// 使用配置文件生成的WebSocket URL订阅任务结果
const ws = new WebSocket('ws://**************:3000/ws');

ws.onopen = () => {
  // 订阅任务
  ws.send(JSON.stringify({
    action: 'subscribeTask',
    taskId: '17819329309638707'
  }));
};

ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  if (data.type === 'token') {
    console.log('收到生成内容:', data.content);
  } else if (data.type === 'done') {
    console.log('推理完成');
  }
};
```

### 生成章节内容（HTTP推理方式）🆕
```bash
curl -X POST "http://localhost:3300/api/v1/novels/1/generate/chapter-content?outline_forks_id=12345&callback_url=https://your-domain.com/webhook/chapter-complete" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token"
```

**返回示例:**
```json
{
  "success": true,
  "message": "章节内容生成任务已启动，基于大纲分支 #12345 生成章节内容",
  "data": {
    "novel_id": 1,
    "generation_type": "chapter_content",
    "content": {
      "chapter_content_id": 98765,
      "novel_id": 1,
      "novel_title": "我的仙侠小说",
      "outline_fork_id": 12345,
      "outline_forks_title": "第1章 - 初入仙途",
      "outline_forks_index": 1,
      "outline_task_id": 67890,
      "outline_index": 1,
      "outline_title": "大纲1：初入仙途",
      "created_at": "2025-06-23T10:30:00",
      "status": "running",
      "websocket_url": "ws://**************:3000/ws",
      "websocket_task_id": "18901234567890123",
      "subscribe_action": "subscribeTask",
      "callback_url": "https://your-domain.com/webhook/chapter-complete"
    },
    "is_complete": false
  }
}
```

**说明:**
- `chapter_content_id`: 章节内容记录ID
- `outline_fork_id`: 大纲分支ID（outline_forks表的id字段）
- `websocket_task_id`: HTTP API创建的推理任务ID
- `websocket_url`: 从配置文件`websocket_subscription`读取的WebSocket URL
- `subscribe_action`: 订阅任务时使用的动作名称

### 查询章节内容
```bash
# 查询单个章节内容
curl "http://localhost:3300/api/v1/novels/1/chapter-content?outline_forks_id=12345" \
  -H "Authorization: Bearer your_jwt_token"

# 查询整个小说的章节内容列表  
curl "http://localhost:3300/api/v1/novels/1/chapter-contents" \
  -H "Authorization: Bearer your_jwt_token"

# 查询大纲任务的章节内容列表
curl "http://localhost:3300/api/v1/novels/1/outline-tasks/67890/chapter-contents" \
  -H "Authorization: Bearer your_jwt_token"
```

### 访问Web客户端
```bash
# 获取客户端信息
curl "http://localhost:3300/api/v1/client-info"

# 直接访问Web界面
open http://localhost:3300/web
```

## Web客户端功能

- **WebSocket连接**: 实时通信，无需轮询
- **多轮对话**: 支持连续对话和上下文
- **流式回复**: 实时显示AI生成内容
- **任务管理**: 任务状态跟踪和重连
- **使用统计**: 实时token消耗监控

## 开发指南

### 配置系统使用
```python
from app.config.configuration import config

# 获取配置值
jwt_secret = config("jwt.secret_key", "default")
db_host = config("database.host", "localhost")
```

### 创建新服务
```python
from app.utils.base_service import BaseService

class MyService(BaseService[MyModel]):
    def __init__(self, db: Session):
        super().__init__(db, MyModel)
    # 自动继承CRUD方法
```

### 添加新路由
```python
from app.utils.error_handlers import handle_api_errors

@router.get("/my-endpoint")
@handle_api_errors
async def my_endpoint():
    return {"message": "success"}
```

### 集成WebSocket推理
```python
from app.utils.inference_websocket import InferenceWebSocketManager

class MyService:
    def generate_content(self, novel_id: int, prompt: str):
        # 保存任务到数据库
        record = self.save_task(novel_id, prompt)
        
        # 启动推理任务
        ws_info = InferenceWebSocketManager.generate_connection_info(str(record.id))
        websocket_task_id = InferenceWebSocketManager.start_inference_task(
            ws_url=ws_info.url,
            prompt=prompt,
            task_id=str(record.id)
        )
        
        return {
            "record_id": record.id,
            "websocket_task_id": websocket_task_id,
            "status": "推理已启动" if websocket_task_id else "推理启动失败"
        }
```

## 特性优势

1. **配置驱动**: 统一的配置管理，支持嵌套访问
2. **代码复用**: 混入类和基础服务减少重复代码
3. **类型安全**: Pydantic确保数据验证和类型安全
4. **错误统一**: 标准化异常处理和响应格式
5. **推理集成**: 完整的WebSocket推理工作流程
6. **智能启动**: 自动资源分配和多平台兼容的启动系统
7. **易于扩展**: 清晰的架构便于功能扩展
8. **开发友好**: 完整的文档和开发工具

## 最新更新

### v2025.06 - 章节内容生成系统完整实现 🆕
- ✅ **章节内容路由**：完整实现章节内容生成、查询API
- ✅ **数据库模型匹配**：ChapterContentDB模型完全匹配数据库表结构
- ✅ **配置化参数**：支持从application.yaml读取生成参数
- ✅ **HTTP推理集成**：使用system+user消息格式调用推理服务
- ✅ **外部回调支持**：完整的callback_url机制，支持任务完成通知
- ✅ **API数据安全**：不返回内部参数（generated_prompt、parameters、template_info）
- ✅ **数据验证**：必需字段验证，确保数据完整性
- ✅ **完整生成流程**：故事结构 → 章节结构 → 大纲 → 章节内容

### v2024.12 - 启动系统优化
- ✅ **智能资源分配**：根据系统CPU、内存和负载自动计算最优进程配置
- ✅ **虚拟环境支持**：自动检测并激活venv虚拟环境
- ✅ **多平台兼容**：专门优化Mac和Ubuntu环境，移除不必要的兼容性代码
- ✅ **简化端口管理**：统一使用lsof进行端口检查和进程管理
- ✅ **监控增强**：改进status和monitor命令，提供详细的性能信息
- ✅ **配置优化**：支持auto值的智能配置，开发和生产环境自动适配

### v2024.12 - HTTP推理系统重构
- ✅ **HTTP推理方式**：改用HTTP API创建推理任务，返回taskId供WebSocket订阅
- ✅ **简化架构**：去除复杂的WebSocket推理逻辑，专注HTTP+订阅模式
- ✅ **重试机制**：HTTP请求支持自动重试，提高成功率
- ✅ **错误处理**：完善的错误分类和处理机制
- ✅ **配置驱动**：WebSocket URL从配置文件读取，支持灵活环境配置
- ✅ **Webhook回调**：推理完成后自动回调更新数据库，无需手动轮询
- ✅ **统一格式**：标准化的API返回格式，包含task_id、websocket_url和subscribe_action
- ✅ **测试验证**：能够正确创建推理任务并返回订阅信息