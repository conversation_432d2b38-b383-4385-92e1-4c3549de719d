# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

AI Novel Generator is a FastAPI-based AI-assisted novel creation platform that helps authors generate, manage, and extract novel content through HTTP inference integration with external LLM services.

## Development Commands

### Running the Application
```bash
# Start the application (recommended method)
bash scripts/run.sh

# Start with custom configuration
PORT=8080 WORKERS=4 bash scripts/run.sh

# Production mode
DEPLOYMENT_MODE=production bash scripts/run.sh

# Other commands
bash scripts/run.sh stop      # Stop application
bash scripts/run.sh restart   # Restart application
bash scripts/run.sh status    # View application status
bash scripts/run.sh monitor   # Monitor with auto-restart
bash scripts/run.sh clean     # Clean Python cache files
```

### Development Environment
```bash
# Setup virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Direct development run (not recommended for production)
python -m app.main
```

### Testing and Health Checks
- API Documentation: `http://localhost:3300/docs`
- Health Check: `http://localhost:3300/health`
- Callback Service Health: `http://localhost:3300/api/v1/callbacks/health`
- Web Client: `http://localhost:3300/web`

## Architecture Overview

### Core Application Structure
- **FastAPI Application** (`app/main.py`): Main entry point with lifespan management
- **Configuration System** (`app/config/`): YAML-based configuration with dot-notation access
- **Router-Service Architecture**: Clear separation between API routes and business logic
- **Middleware**: JWT authentication and CORS handling
- **Database Models**: SQLAlchemy with mixin classes for common fields

### Key Architectural Patterns

#### Configuration Management
```python
from app.config.configuration import config
jwt_secret = config("jwt.secret_key", "default")
db_host = config("database.host", "localhost")
```

#### Service Layer Pattern
All services inherit from `BaseService` for CRUD operations:
```python
from app.utils.base_service import BaseService

class MyService(BaseService[MyModel]):
    def __init__(self, db: Session):
        super().__init__(db, MyModel)
    # Automatically inherits CRUD methods
```

#### Error Handling
Standardized error handling using decorators:
```python
from app.utils.error_handlers import handle_api_errors

@router.get("/endpoint")
@handle_api_errors
async def endpoint():
    return {"message": "success"}
```

### Novel Generation Workflow
1. **Story Structure Generation** → Creates overall story framework
2. **Chapter Structure Generation** → Defines chapter breakdown 
3. **Outline Generation** → Detailed chapter outlines with forks/branches
4. **Chapter Content Generation** → Full chapter text content

### HTTP Inference Integration
The system uses HTTP API calls to create inference tasks, then provides WebSocket URLs for subscribing to results:

```python
from app.utils.inference_websocket import InferenceManager

# Create inference task via HTTP
messages = [{'role': 'user', 'content': 'Generate story'}]
response = InferenceManager.create_inference_task(messages)

# Returns: task_id, websocket_url, subscribe_action for client subscription
```

#### Callback System
- Automatic webhook setup for inference completion
- Retry mechanism with exponential backoff (5 attempts max)
- Callbacks update database records when inference completes
- Callback endpoints: `/api/v1/callbacks/{type}/{task_id}`

### Database Architecture
- **Mixin Classes** (`app/models/mixins.py`): Common fields like timestamps, snowflake IDs
- **Base Service Class** (`app/utils/base_service.py`): Generic CRUD operations
- **Snowflake ID System** (`app/utils/snowflake.py`): Distributed unique ID generation

### Configuration Files
- `app/config/application.yaml`: Main configuration including HTTP inference, WebSocket subscription, JWT settings
- Environment variables override YAML values
- Smart resource allocation based on system specs (CPU/memory)

## Important Development Notes

### Resource Management
The startup script (`scripts/run.sh`) automatically calculates optimal worker processes and callback service parameters based on:
- CPU cores and available memory
- Deployment mode (development vs production)  
- System load
- Supports both single-process and multi-process deployment

### Authentication System
- JWT-based authentication with configurable expiration
- Development mode supports auto-login and bypassing certain validations
- Middleware handles token validation across all protected routes

### Novel Content Structure
The system handles complex novel structures with:
- **Forks/Branches**: Multiple versions of story elements (chapters, outlines)
- **Hierarchical Organization**: Novels → Story Structure → Chapter Structure → Outlines → Chapter Content
- **Configurable Parameters**: Chapter count, word targets, writing styles via YAML

### Key Utilities
- **User Context Tracking** (`app/utils/user_context.py`): Request-level user information
- **Common Helpers** (`app/utils/common_helpers.py`): Shared utility functions  
- **Inference WebSocket Manager** (`app/utils/inference_websocket.py`): HTTP inference task creation and WebSocket coordination

### Testing HTTP Inference
```python
# Test HTTP inference API
python -c "
from app.utils.inference_websocket import InferenceManager
messages = [{'role': 'user', 'content': 'Write a poem'}]
response = InferenceManager.create_inference_task(messages)
print(f'Task ID: {response.task_id}')
print(f'WebSocket URL: {response.websocket_url}')
"

# Health check
python -c "
from app.utils.inference_websocket import InferenceManager
status = InferenceManager.health_check()
print(f'Service Status: {\"Healthy\" if status else \"Unhealthy\"}')
"
```